/******************************
Node environment setup
******************************/

NODE_ENV=Development

/******************************
db and sequelize are used for database connection (database.js file)
******************************/

db = **************************************/DBNAME

/******************************
Api listening port (server.js file)
******************************/

serverPort = 4000

/******************************
Secret keys these are used in token generation (globals.js file)
******************************/

sessionSecret= indNIC2305
securityToken= indNIC2305
securityRefreshToken= indNIC2305refresh

/******************************
we append baseApiUrl infront of api endpoints (in all routes.js file)
******************************/

baseApiUrl= /api

/******************************
apiUrl of the application
******************************/

apiUrl= http://[host]:[port]

/******************************
rootUrl (apiUrl+ baseApiUrl) of the application
******************************/

rootUrl= http://[host]:[port]/api

/******************************
FrontUrl's, in emails we use these url's in links, when use click on that link it will redirect to that front page.
******************************/

frontUrl= http://[host]:[port]
frontUrlAngular= http://[host]:[port]

/******************************
defaultEmailId (from mail of the email) (email.js)
******************************/

defaultEmailId= <EMAIL>
  
/******************************
localImagePath (images will upload in this path) (file.js)
******************************/

localImagePath: /public/upload/images/

/******************************
s3upload (if we change s3upload to true then images will upload on s3 server, until unless upload in local server)
******************************/
s3upload= false
s3ImagePath= " "

/******************************
user security (based on the keys, action will perform) (globals.js)
******************************/

dontAllowPreviouslyUsedPassword= true
storePreviouslyUsedPasswords= false

forceToUpdatePassword= true

 /***** In months *****/
updatePasswordPeriod= 4

allowedFailAttemptsOfLogin= 5
isBlockAfterFailedAttempt= true

/***** In minutes *****/
timeDurationOfBlockingAfterWrongAttempts= 15 

/******************************
Different types of token expiration times and token extension permissions (globals.js)
******************************/
 /***** In minutes *****/
tokenExpirationTime= 540 

 /***** In minutes *****/
forgotTokenExpireTime= 60 

 /***** In minutes *****/
verificationTokenExpireTime= 60
 
/***** Note: in seconds! (1 day) *****/
tokenExpiry= 361440

extendTokenTime= true
useRefreshToken= true

/******************************
These keys are used for swagger implementation (server.js file)
******************************/

host = [host]:[port]
isHTTPAuthForSwagger= true
HTTPAuthUser= indianic
HTTPAuthPassword= indianic



