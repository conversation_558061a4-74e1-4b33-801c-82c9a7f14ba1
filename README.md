[![pipeline status](http://git.indianic.com/IIL0/I2022-6239/javascript-frameworks-nodejs-postgresql/badges/dev/pipeline.svg)](http://git.indianic.com/IIL0/I2022-6239/javascript-frameworks-nodejs-postgresql/commits/dev)

# IndiaNIC Universal-Admin Node+Postgresql Project

## Prequisites (Development):

| Module | Version |
| --- |---------|
| Node |20.17.0 |
| Npm | 10.8.2 |
| sequelize | 6.37.3  |


### Running Project In Local
``` bash
$ git clone http://git.indianic.com/IIL0/I2024-6439/node-js-devhousiebackend-/tree/dev

$ git checkout dev(checkout to new branch name following coding standards )

$ mv .env.sample .env

$ vi .env (need to change environment related details)

$ npm install

$ npm run seed (for seeding database)

$ npm run sync-labels (for syncing language error messages)

$ node server.js

```
------------

### Scripts
$ npm run sync-labels (for seeding error messages for all languages)

------------

## Code Quality Check (Mac OS)

> Install homebrew : https://docs.brew.sh/Installation

> brew install sonarqube

> brew services start sonarqube 

> brew install sonar-scanner

> Open http://localhost:9000 login using mentioned id and password 

> Configured "sonar-project.js" and "sonar-project.properties" file on your project root directory 

> Configured sonar scripts in package.json and run scripts to start analysis of project

## Code Quality Check

> Download Java Version 11

> Download SonarQuebe from https://www.sonarqube.org/downloads/

> Please add below line at end of the file soanrqube-7.9.1 > conf > sonar.properties #sonar.host.url=http://localhost:9000

> Install sonarqube-scanner from https://www.npmjs.com/package/sonarqube-scanner

> Follow Steps given in this link https://yuriburger.net/2017/09/27/getting-started-with-sonarqube-and-typescript/ Except Rules

> Command to start sonarquebe serving on localhost:9000 $ sonarqube-7.9.1/bin/macosx-universal-64/sonar.sh start

> Start analysis of project with following command $ sonar-scanner


## Deployment In Staging Server

``` bash
$ git clone -b qa http://git.indianic.com/IIL0/I2024-6439/node-js-devhousiebackend-/tree/dev

$ cd configs

$ mv configSample.js configs.js

$ vi configs.js (need to change environment)

$ cd .. 

$ mv .env.sample .env.qa(file name should be based on environment dev/qa/uat/production)

$ vi .env.qa (need to change environment related details)

$ cd ..

$ sh package.sh

```
------------

## Deployment With CI/CD
> Coming Soon

## Node Coding Standards

> You can get more information on node coding stantdards from [here](https://docs.google.com/document/d/1_ejxCdzwZzWLrhy1xPmzSh8mt7pnz1p50vuHQSnVcXE/edit).

##### Directory Structure
```
|-- CommonSeedV16.17.1/
    |-- app/
        |-- locales/
            |-- de.json
            |-- en.json
            |-- es.json
        |-- modules/
            |-- User/
                |-- Controller.js
                |-- Projection.json
                |-- Routes.js
                |-- Schema.js
                |-- swagger.json
                |-- Validator.js
           |-- ...
        |-- services/
            |-- Common.js
            |-- Constant.js
            |-- Email.js
            |-- Database.js
            |-- Encryption.js
            |-- Form.js
            |-- Logger.js
    |-- configs/
        |-- commonlyUsedPassword.json
        |-- configs.js
        |-- express.js
        |-- Globals.js
        |-- database.js
        |-- P8Apns.p8
    |-- node_modules/
    |-- public/
        |-- style.css
        |-- password-reset.html
    |-- .env.sample
    |-- README.md
    |-- .gitignore
    |-- server.js
    |-- package.json
    |-- package-lock.json
    |-- sonar-project.js
    |-- swagger.json
       
``` 
-------------