const _ = require("lodash");
const i18n = require("i18n");
const Controller = require("../Base/Controller");
const { currencySchema } = require("./Schema");
class CurrencyController extends Controller {
  constructor() {
    super();
  }

  async addCurrency() {
    try {
      await currencySchema.create(this.req.body);

      return this.res.send({
        status: 1,
        message: i18n.__("CURRENCY_ADDED_SUCCESSFULLY"),
      });
    } catch (error) {
      /********************************************************
        Manage Error logs and Response
        ********************************************************/
      console.error("error addCurrency()", error);
      return this.res.send({ status: 0, message: i18n.__("SERVER_ERROR") });
    }
  }

  async getAllCurrency() {
    try {
      const { type } = this.req.query;

      const VALID_TYPES = new Set(["coin", "gem"]);

      if (!VALID_TYPES.has(type)) {
        return this.res.send({
          status: 0,
          message: !type
            ? i18n.__("TYPE_IS_REQUIRED")
            : i18n.__("INVALID_COIN_TYPE"),
        });
      }
      const currencyList = await currencySchema.findAll({
        where: {
          isDeleted: false,
          type,
        },
        attributes: ["_id", "numberOfQty", "price", "type"],
        raw: true,
      });

      if (!currencyList.length) {
        return this.res.send({
          status: 0,
          message: i18n.__("NO_DATA_FOUND"),
        });
      }

      return this.res.send({
        status: 1,
        message: i18n.__("SUCCESS"),
        data: currencyList,
      });
    } catch (error) {
      /********************************************************
        Manage Error logs and Response
        ********************************************************/
      console.error("error getAllCurrency()", error);
      return this.res.send({ status: 0, message: i18n.__("SERVER_ERROR") });
    }
  }

  async deleteCurrency() {
    try {
      const { id } = this.req.params;

      const [affectedCount] = await currencySchema.update(
        {
          isDeleted: true,
        },
        {
          where: {
            _id: id,
            isDeleted: false,
          },
        }
      );

      if (!affectedCount) {
        return this.res.send({
          status: 0,
          message: i18n.__("CURRENCY_NOT_FOUND"),
        });
      }

      return this.res.send({
        status: 1,
        message: i18n.__("CURRENCY_DELETED_SUCCESSFULLY"),
      });
    } catch (error) {
      /********************************************************
        Manage Error logs and Response
        ********************************************************/
      console.error("error deleteCurrency()", error);
      return this.res.send({ status: 0, message: i18n.__("SERVER_ERROR") });
    }
  }
}

module.exports = CurrencyController;
