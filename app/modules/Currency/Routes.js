module.exports = (app, express) => {
  const router = express.Router();
  const Globals = require("../../../configs/Globals");
  const CurrencyController = require("./Controller");
  const Middleware = require("../../services/middleware");
  const Validators = require("./Validator");
  const config = require("../../../configs/configs");

  router.post(
    "/currency",
    Globals.isUserAuthorized(),
    Validators.addCurrency(),
    Middleware.validateBodyApp,
    (req, res) => {
      const currencyObj = new CurrencyController().boot(req, res);
      return currencyObj.addCurrency();
    }
  );

  router.get("/currency", Globals.isUserAuthorized(), (req, res) => {
    const currencyObj = new CurrencyController().boot(req, res);
    return currencyObj.getAllCurrency();
  });

  router.delete("/currency/:id", Globals.isUserAuthorized(), (req, res) => {
    const currencyObj = new CurrencyController().boot(req, res);
    return currencyObj.deleteCurrency();
  });

  app.use(config.baseApiUrl, router);
};
