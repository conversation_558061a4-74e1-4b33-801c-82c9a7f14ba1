const _ = require("lodash");
const { DataTypes } = require("sequelize");
const { sequelizeConnection } = require("../../../configs/database");

let currencySchema = sequelizeConnection.define("currency", {
  _id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  numberOfQty: { type: DataTypes.INTEGER, allowNull: true },
  price: { type: DataTypes.INTEGER },
  type: { type: DataTypes.ENUM("coin", "gem") },
  isDeleted: { type: DataTypes.BOOLEAN, defaultValue: false },
});

module.exports = {
  currencySchema,
};
