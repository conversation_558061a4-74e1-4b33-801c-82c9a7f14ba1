/****************************
 Validators
 ****************************/
const _ = require("lodash");
const Joi = require("joi");
const i18n = require("i18n");

class Validators {
  static addCurrency() {
    return async (req, res, next) => {
      try {
        req.schema = Joi.object().keys({
          numberOfQty: Joi.number()
            .required()
            .error(new Error(i18n.__("JOI_VALID_NUMBER_OF_QTY"))),
          price: Joi.number()
            .required()
            .error(new Error(i18n.__("JOI_VALID_PRICE"))),
          type: Joi.string()
            .valid("coin", "gem")
            .required()
            .error(new Error(i18n.__("JOI_VALID_TYPE"))),
        });
        next();
      } catch (error) {
        throw new Error(error);
      }
    };
  }
}

module.exports = Validators;
