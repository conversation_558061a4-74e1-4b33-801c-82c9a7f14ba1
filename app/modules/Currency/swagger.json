{"paths": {"/currency": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "description": "Add <PERSON>cy", "parameters": [{"name": "body", "in": "body", "description": "Add <PERSON>cy", "schema": {"$ref": "#/definitions/AddCurrencyReq"}}, {"name": "Authorization", "in": "header", "description": "Authorization", "type": "string", "required": true}, {"name": "deviceid", "in": "header", "description": "deviceid of device android, ios or web", "type": "string", "required": true}], "security": {"basicAuth": []}, "produces": ["application/json"], "responses": {"200": {"description": "Add currency response"}}}, "get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "description": "<PERSON>urrency", "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "type": "string", "required": true}, {"name": "deviceid", "in": "header", "description": "deviceid of device android, ios or web", "type": "string", "required": true}, {"name": "type", "in": "query", "description": "type of currency gem or coin", "type": "string", "required": true}], "security": {"basicAuth": []}, "produces": ["application/json"], "responses": {"200": {"description": "Add currency response"}}}}, "/currency/{id}": {"delete": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "description": "Delete Currency", "parameters": [{"name": "id", "in": "path", "description": "id of currency", "type": "string", "required": true}, {"name": "Authorization", "in": "header", "description": "Authorization", "type": "string", "required": true}, {"name": "deviceid", "in": "header", "description": "deviceid of device android, ios or web", "type": "string", "required": true}], "security": {"basicAuth": []}, "produces": ["application/json"], "responses": {"200": {"description": "Delete currency response"}}}}}, "definitions": {"AddCurrencyReq": {"required": ["numberOfQty", "price", "type"], "properties": {"numberOfQty": {"type": "number", "example": 10, "required": true}, "price": {"type": "number", "example": 100, "required": true}, "type": {"type": "string", "example": "gem || coin", "required": true}}}, "UpdateUserProfileReq": {"required": ["avatar", "name"], "properties": {"avatar": {"type": "string", "example": "FeralShadow", "required": true}, "name": {"type": "string", "example": "user0001", "required": true}, "country": {"type": "string", "example": "India", "required": true}}}}}