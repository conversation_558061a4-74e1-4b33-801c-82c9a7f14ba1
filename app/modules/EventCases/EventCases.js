const { EVENTS_NAME, COMMON } = require("../../services/constant");
const { createTable } = require("../ManageTables/createTable");
const { findTable } = require("../ManageTables/findTable");
const { logger } = require("../../services/logger");
const generatePrivateGameCode = require("./generatePrivateGameCode");

const eventCases = async (socket) => {
  try {
    socket.onAny(async (eventName, payload, ack) => {
      logger.log("Received event:", eventName);

      if (typeof payload === "string") {
        payload = payload.trim();
        if (!payload) {
          socket.emit(EVENTS_NAME.DEFAULT, {
            data: { message: "Payload is not correct." },
          });
          return;
        }
        payload = JSON.parse(payload);
      }

      switch (eventName) {
        case EVENTS_NAME.JOIN_TABLE:
          logger.log(`Processing CREATE_TABLE event`, payload);
          await findTable(socket, payload);
          socket.emit(EVENTS_NAME.CREATE_TABLE, {
            data: { message: "Success Fully Created Table" },
          });
          break;

        case EVENTS_NAME.GENERATE_PRIVATE_GAME_CODE:
          logger.log(`Generating PRIVATE_GAME_CODE`);
          const code = generatePrivateGameCode();
          socket.emit(EVENTS_NAME.GENERATE_PRIVATE_GAME_CODE, {
            data: { message: "Success Fully Generated Code", code },
          });
          break;
        default:
          logger.log(`Unknown event: ${eventName}`);
          socket.emit(EVENTS_NAME.DEFAULT, {
            data: { message: COMMON.UNKNOWN_EVENT },
          });
          break;
      }
    });
  } catch (error) {
    logger.error("Error in eventCases:", error);
    socket.emit(EVENTS_NAME.DEFAULT, {
      data: { message: COMMON.ERROR_OCCURRED },
      error: error.message,
    });
  }
};

module.exports = {
  eventCases,
};
