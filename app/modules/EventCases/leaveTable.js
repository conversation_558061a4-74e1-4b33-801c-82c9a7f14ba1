const { EVENTS_NAME } = require("../../services/constant");
const { logger } = require("../../services/logger");
const socketIOConnection = require("../../../server");
//const { applyTableLock, removeTableLock } = require("../common/locks/table");
const { removeUserFromTable } = require("../gameActivity/removeUserFromTable");
//const { applyMatchMakingLock, removeMatchMakingLock } = require("../common/locks/matchMaking");

const leaveTable = async (socket, leaveData) => {
    // const matchMakingLock = await applyMatchMakingLock("leaveTable", socket.handshake.auth?.bootValue, 2);

    // const tableLock = await applyTableLock("leaveTable", socket.handshake.auth?.tableId, 2);

    try {
        logger.log("leaveTable", { socketData: socket.handshake.auth, leaveData });

        await removeUserFromTable(socket.id, socket.handshake.auth?.tableId, socket.handshake.auth?.userId, "");

    } catch (error) {
        socketIOConnection.emit(EVENTS_NAME.ERROR_POPUP, { 
            socketId: socket.id, 
            data: { message: error?.message } 
        });

        logger.errorLog("leaveTable Error : ", error);

    } finally {
        // if (matchMakingLock) { 
        //     await removeMatchMakingLock("leaveTable", matchMakingLock); 
        // }

        // if (tableLock) { 
        //     await removeTableLock("leaveTable", tableLock); 
        // }
    }
};

module.exports = { leaveTable };