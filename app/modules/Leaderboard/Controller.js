const _ = require("lodash");
const i18n = require("i18n");
const Controller = require("../Base/Controller");
const { transactionSchema } = require("./Schema");
const { Op, fn, cast, col, literal } = require("sequelize");
const { appUserSchema } = require("../Users/<USER>");

class LeaderboardController extends Controller {
  constructor() {
    super();
  }

  async getLeaderBoard() {
    try {
      const { type } = this.req.query;
      const page = parseInt(this.req.query.page) || 1;
      const limit = parseInt(this.req.query.limit) || 30;
      const offset = (page - 1) * limit;
      const VALID_TYPES = new Set(["daily", "weekly", "monthly"]);

      if (!VALID_TYPES.has(type)) {
        return this.res.send({
          status: 0,
          message: !type
            ? i18n.__("TYPE_IS_REQUIRED")
            : i18n.__("INVALID_TYPE"),
        });
      }

      let startDate;
      const now = new Date();

      if (type === "daily") {
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        startDate.setHours(0, 0, 0, 0);
      } else if (type === "weekly") {
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - startDate.getDay());
        startDate.setHours(0, 0, 0, 0);
      } else if (type === "monthly") {
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        startDate.setHours(0, 0, 0, 0);
      }

      let leaderboard = await transactionSchema.findAll({
        offset,
        limit,
        where: {
          createdAt: {
            [Op.gt]: startDate,
          },
          currencyType: "coin",
          reason: "win",
          isDeleted: false,
        },
        attributes: [
          "userId",
          [fn("SUM", cast(col("qty"), "INTEGER")), "totalcoins"],  
        ],
        group: ["userId","user._id"],
        order: [[literal("totalcoins"), "DESC"]],  
        limit: 30,
        include: [
          {
            model: appUserSchema,
            as: "user",
            attributes: ["_id", "name", "avatar"],
          },
        ],
      });

      const result = leaderboard.map((entry) => ({
        userId: entry.user._id,
        username: entry.user?.name || "",
        avatar: entry.user.avatar || "",
        totalCoins: parseInt(entry.get("totalCoins") || 0, 10),
      }));

      return this.res.send({
        status: 1,
        message: i18n.__("SUCCESS"),
        data: result,
        pagination: { page, limit },
      });
    } catch (error) {
      /********************************************************
        Manage Error logs and Response
        ********************************************************/
      console.error("error getLeaderBoard()", error);
      return this.res.send({ status: 0, message: i18n.__("SERVER_ERROR") });
    }
  }
}

module.exports = LeaderboardController;
