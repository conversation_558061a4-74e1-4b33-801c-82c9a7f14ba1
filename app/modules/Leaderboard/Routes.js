module.exports = (app, express) => {
  const router = express.Router();
  const Globals = require("../../../configs/Globals");
  const LeaderboardController = require("./Controller");
  const config = require("../../../configs/configs");

  router.get("/leaderboard",Globals.isUserAuthorized(),(req, res)=>{
    const leaderboardObj = new LeaderboardController().boot(req, res);
    return leaderboardObj.getLeaderBoard();
  })

  app.use(config.baseApiUrl, router);
};
