const _ = require("lodash");
const { DataTypes } = require("sequelize");
const { sequelizeConnection } = require("../../../configs/database");
const { appUserSchema } = require("../Users/<USER>");

let transactionSchema = sequelizeConnection.define("transaction", {
  _id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  userId: { type: DataTypes.UUID },
  qty: { type: DataTypes.NUMBER },
  currencyType: { type: DataTypes.ENUM("gem", "coin") },
  reason: { type: DataTypes.ENUM("win", "bonus", "referral", "purchase") },
  isDeleted: { type: DataTypes.BOOLEAN, defaultValue: false },
});

transactionSchema.belongsTo(appUserSchema, { foreignKey: "userId" });
appUserSchema.hasMany(transactionSchema, { foreignKey: "userId" });

module.exports = {
  transactionSchema,
};
