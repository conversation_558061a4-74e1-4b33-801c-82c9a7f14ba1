/****************************
 Validators
 ****************************/
const _ = require("lodash");
const Joi = require("joi");
const i18n = require("i18n");

class Validators {
  static loginValidate() {
    return async (req, res, next) => {
      try {
        req.schema = Joi.object().keys({
          deviceToken: Joi.string()
            .required()
            .error(new Error(i18n.__("JOI_VALID_DEVICE_TOKEN"))),
        });
        next();
      } catch (error) {
        throw new Error(error);
      }
    };
  }
}

module.exports = Validators;
