{"paths": {"/leaderboard": {"get": {"tags": ["Leaderboard"], "description": "Get Leaderboard", "parameters": [{"name": "type", "in": "query", "description": "Leaderboard Type", "type": "string", "required": true, "example": "daily || weekly || monthly"}, {"name": "page", "in": "query", "description": "Page Number", "type": "number", "required": false, "example": 1}, {"name": "limit", "in": "query", "description": "Page Limit", "type": "number", "required": false, "example": 10}, {"name": "Authorization", "in": "header", "description": "Authorization", "type": "string", "required": true}, {"name": "deviceid", "in": "header", "description": "deviceid of device android, ios or web", "type": "string", "required": true}], "security": {"basicAuth": []}, "produces": ["application/json"], "responses": {"200": {"description": "Join as guest response"}}}}}, "definitions": {"UpdateUserProfileReq": {"required": ["avatar", "name"], "properties": {"avatar": {"type": "string", "example": "FeralShadow", "required": true}, "name": {"type": "string", "example": "user0001", "required": true}, "country": {"type": "string", "example": "India", "required": true}}}}}