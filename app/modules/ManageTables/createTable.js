const { dummyTable, dummyPrivateTable } = require("../dummyData/table");
const { dummyTableUser } = require("../dummyData/tableUser");
const { dummyUserInTable, dummyUserInPrivateTable } = require("../dummyData/userInTable");
const { joinRoom } = require("../SocketOperations/joinRoom");
const { setTable, setPrivateTable } = require("../gameRedisOperations/table");
const { setUserInTable, setUserInPrivateTable } = require("../gameRedisOperations/userInTable");
const { setUser } = require("../gameRedisOperations/user");
const { setEmptyTable, setPrivateEmptyTable } = require("../gameRedisOperations/emptyTable");
const { logger } = require("../../services/logger");
const { EVENTS_NAME } = require("../../services/constant");

const createTable = async (socket, user) => {
  try {
    logger.log("createTable", { user });

    const table = await dummyTable(); // TableInterface

    const tableUser = await dummyTableUser(user, 0);

    const userInTable = await dummyUserInTable(
      user._id,
      table.tableId,
      0,
      socket.id
    ); // UserInTableInterface

    table.users.push(tableUser);

    await Promise.all([
      joinRoom(socket, table?.tableId),
      setTable(table.tableId, table),
      setEmptyTable(table.tableId),
      setUserInTable(table.tableId, user._id, userInTable),
      setUser(user._id, { ...user, tableId: table.tableId }),
    ]);

    socket.emit(EVENTS_NAME.JOIN_TABLE, {
      roomId: table.tableId,
      data: table,
    });

    socket.handshake.auth.tableId = table.tableId;
    socket.handshake.auth.seatIndex = userInTable.seatIndex;
  } catch (error) {
    logger.errorLog("createTable Error : ", error);
  }
};

const createPrivateTable = async (socket, user, payload) => {
  try {

    logger.log("createPrivateTable", { user });

    const table = await dummyPrivateTable(payload); 

    const tableUser = await dummyTableUser(user, 0);

    const userInTable = await dummyUserInPrivateTable(
      user._id,
      table.tableId,
      0,
      socket.id
    ); // UserInTableInterface

    table.users.push(tableUser);

    await Promise.all([
      joinRoom(socket, table?.tableId),
      setPrivateTable(table.tableId, table),
      setPrivateEmptyTable(table.tableId),
      setUserInPrivateTable(table.tableId, user._id, userInTable),
      setUser(user._id, { ...user, tableId: table.tableId }),
    ]);

    socket.emit(EVENTS_NAME.JOIN_TABLE, {
      roomId: table.tableId,
      data: table,
    });

    socket.handshake.auth.tableId = table.tableId;
    socket.handshake.auth.seatIndex = userInTable.seatIndex;
  } catch (error) {
    logger.errorLog("createTable Error : ", error);
  }
};

module.exports = { createTable, createPrivateTable };
