const { joinTable } = require("./joinTable");
const { createTable, createPrivateTable } = require("./createTable");
const { logger } = require("../../services/logger");
const { getTable } = require("../gameRedisOperations/table");
const {
  getEmptyTable,
  getPrivateTable,
} = require("../gameRedisOperations/emptyTable");
const { validatePrivateTable } = require("../validation/privateTable");

const findTable = async (socket, payload) => {
  try {
    if (payload.isPrivate) {
      logger.log("findTable", { payload });
      const emptyTables = await getPrivateTable(payload.privateTableCode);

      console.log("++++++++++++++++++++++++++",emptyTables);   
      if (emptyTables) {
      } else {
        const validateError = await validatePrivateTable(payload);

        if (validateError) {
          throw new Error(validateError);
        }
        console.log("****************************");
        await createPrivateTable(socket, socket.user, payload);
      }
    } else {
      logger.log("findTable", { payload });
      const emptyTables = await getEmptyTable();
      if (emptyTables.length) {
        for (let i = 0; i < emptyTables.length; i++) {
          const tableData = await getTable(emptyTables[i].split(":")[1]).catch(
            (error) => {
              logger.errorLog("findTable emptyTables Error : ", error);
            }
          );
          if (
            !tableData ||
            // tableData.isTableLock ||
            tableData.isGameStart
            //tableData.isWinning
          ) {
            continue;
          }
          const tableMatch = await joinTable(
            socket,
            socket.user,
            tableData.tableId
          );

          if (tableMatch) {
            return;
          }
        }

        await createTable(socket, socket.user);
      } else {
        await createTable(socket, socket.user);
      }
    }
  } catch (error) {
    logger.errorLog("findTable Error : ", error);
  }
};

module.exports = { findTable };
