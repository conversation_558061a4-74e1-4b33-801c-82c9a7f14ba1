const config = require("../../../configs/configs");
const { getTable, setTable } = require("../gameRedisOperations/table");
const { dummyTableUser } = require("../dummyData/tableUser");
const { setUserInTable } = require("../gameRedisOperations/userInTable");
const { dummyUserInTable } = require("../dummyData/userInTable");
const { joinRoom } = require("../SocketOperations/joinRoom");
const { setUser } = require("../gameRedisOperations/user");
const { EVENTS_NAME } = require("../../services/constant");
const { deleteEmptyTable } = require("../gameRedisOperations/emptyTable");
const { logger } = require("../../services/logger");
const joinTable = async (socket, user, tableId) => {
  //const tableLock = await applyTableLock("joinTable", tableId ?? "", 2);

  try {
    if (!tableId) {
      throw new Error("Table Id Not Found !!!");
    }
    const tableData = await getTable(tableId);

    const maxUsers = config.gamePlay.MAX_USERS;
    const availableSeats = [];

    for (let index = 0; index < maxUsers; index++) {
      const isSeatTaken = tableData.users.some(
        (user) => user.seatIndex === index
      );
      if (!isSeatTaken) {
        availableSeats.push(index);
      }
    }

    const availableSeat = availableSeats[0];

    if (availableSeat === undefined) {
      return false;
    }

    const tableUser = await dummyTableUser(user, availableSeat);

    const userInTable = await dummyUserInTable(
      user._id,
      tableId,
      availableSeat,
      socket.id
    );

    tableData.users = [...tableData.users, tableUser].sort(
      (first, second) => first.seatIndex - second.seatIndex
    );

    socket.handshake.auth.tableId = tableId;
    socket.handshake.auth.seatIndex = userInTable.seatIndex;

    await Promise.all([
      joinRoom(socket, tableId),

      setUser(user._id, { ...user, tableId }),

      setUserInTable(tableId, user._id, userInTable),

      setTable(tableId, {
        ...tableData,
        isGameStart: true,
        isRoundTimer: true,
      }),
    ]);

    socket.emit(EVENTS_NAME.JOIN_TABLE, { roomId: tableId, data: tableData });
    console.log("tableData.users.length", tableData.users);
    if (tableData.users.length === 5) {
      await deleteEmptyTable(tableId);
    }

    if (!tableData.isGameStart) {
      socket.emit(EVENTS_NAME.GAME_START, {
        roomId: tableId,
        data: { timer: config.gamePlay.GAME_START_TIMER },
      });

      //await Promise.all([gameStartAdd(tableId), tableLockAdd(tableId)]);
    } else {
      // const timeDiff = await remainingBullTime(await gameStartGet(tableId), 0);
      // eventEmitter.emit(CONSTANTS.EVENTS_NAME.GAME_START, { roomId: socket.id, data: { timer: timeDiff } });
    }

    return true;
  } catch (error) {
    logger.errorLog("joinTable Error : ", error);
  } finally {
    //if (tableLock) { await removeTableLock("joinTable", tableLock); };
  }
};

module.exports = { joinTable };
