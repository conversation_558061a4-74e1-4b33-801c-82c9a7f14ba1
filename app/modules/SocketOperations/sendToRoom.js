const socketIOConnection = require("../../../server")
import { logger } from "../../services/logger";

const sendToRoom = async (EVENT, Data) => {

    try {

        const { roomId, data } = Data;

        logger.log("sendToRoom", { EVENT, data });

        socketIOConnection.to(roomId).emit(EVENT, JSON.stringify({ ...data }));

    } catch (error) {
        logger.errorLog("sendToRoom Error : ", error);
    };
};

module.exports =  { sendToRoom };