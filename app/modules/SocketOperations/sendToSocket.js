const socketIOConnection = require("../../../server");
const { EVENTS_NAME } = require("../../services/constant");
const { logger } = require("../../services/logger");

const sendToSocket = async (EVENT, Data) => {
  try {
    const { socketId, data } = Data;

    if (EVENTS_NAME.HEART_BEAT !== EVENT) {
      logger.log("sendToSocket", { EVENT, data });
    }

    socketIOConnection.to(socketId).emit(EVENT, JSON.stringify({ ...data }));
  } catch (error) {
    logger.errorLog("sendToSocket Error : ", error);
  }
};

export { sendToSocket };
