const _ = require("lodash");
const { DataTypes } = require("sequelize");
const { sequelizeConnection } = require("../../../configs/database");
const { appUserSchema } = require("../Users/<USER>");

let staticsSchema = sequelizeConnection.define("statics", {
  _id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  userId: { type: DataTypes.UUID },
  earlyFive: { type: DataTypes.NUMBER, defaultValue: 0 },
  firstRow: { type: DataTypes.NUMBER, defaultValue: 0 },
  secondRow: { type: DataTypes.NUMBER, defaultValue: 0 },
  thirdRow: { type: DataTypes.NUMBER, defaultValue: 0 },
  fullHouse: { type: DataTypes.NUMBER, defaultValue: 0 },
  totalMatches: { type: DataTypes.NUMBER, defaultValue: 0 },
  isDeleted: { type: DataTypes.BOOLEAN, defaultValue: false },
});

staticsSchema.belongsTo(appUserSchema, { foreignKey: "userId" });

module.exports = {
  staticsSchema,
};
