{"paths": {"/join-as-guest": {"post": {"tags": ["Users"], "description": "Sign in as a guest", "parameters": [{"name": "body", "in": "body", "description": "Sign in", "schema": {"$ref": "#/definitions/SignInAsGuestReq"}}, {"name": "deviceid", "in": "header", "description": "deviceid of device android, ios or web", "type": "string", "required": true}], "security": {"basicAuth": []}, "produces": ["application/json"], "responses": {"200": {"description": "Join as guest response"}}}}, "/profile": {"put": {"tags": ["Users"], "description": "Update User Profile", "parameters": [{"name": "body", "in": "body", "description": "Update User Profile", "schema": {"$ref": "#/definitions/UpdateUserProfileReq"}}, {"name": "Authorization", "in": "header", "description": "Authorization", "type": "string", "required": true}, {"name": "deviceid", "in": "header", "description": "deviceid of device android, ios or web", "type": "string", "required": true}], "security": {"basicAuth": []}, "produces": ["application/json"], "responses": {"200": {"description": "Update user profile response"}}}, "get": {"tags": ["Users"], "description": "Get User Profile", "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "type": "string", "required": true}, {"name": "deviceid", "in": "header", "description": "deviceid of device android, ios or web", "type": "string", "required": true}], "security": {"basicAuth": []}, "produces": ["application/json"], "responses": {"200": {"description": "Update user profile response"}}}, "delete": {"tags": ["Users"], "description": "Delete User Profile", "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "type": "string", "required": true}, {"name": "deviceid", "in": "header", "description": "deviceid of device android, ios or web", "type": "string", "required": true}], "security": {"basicAuth": []}, "produces": ["application/json"], "responses": {"200": {"description": "Delete user profile response"}}}}, "/user/socialLoginAndRegister": {"post": {"tags": ["Users"], "summary": "Social Login And Register", "description": "Social Login And Register API", "parameters": [{"name": "deviceid", "in": "header", "description": "deviceid of device android, ios or web", "type": "string", "required": true}, {"in": "body", "name": "body", "required": true, "schema": {"type": "object", "required": ["socialId", "socialType", "deviceToken", "idToken"], "properties": {"socialId": {"type": "string", "example": "48s7ad5s18d584de1dx5sdx15sx18w74d5d1xd8d5d145df450"}, "socialType": {"type": "string", "example": "(google/apple)"}, "deviceToken": {"type": "string", "example": "48s7ad5s18d584de1dx5sdx15sx18w74d5d1xd8d5d145df450"}, "idToken": {"type": "string", "example": "48s7ad5s18d584de1dx5sdx15sx18w74d5d1xd8d5d145df450"}}}}], "responses": {"200": {"description": "Social Login And Register details", "schema": {"$ref": "#/definitions/SocialLoginAndRegisterRes"}}}}}, "/add-currency": {"put": {"tags": ["Users"], "summary": "Update user currency", "description": "Update user currency", "parameters": [{"name": "Authorization", "in": "header", "description": "Authorization", "type": "string", "required": true}, {"name": "deviceid", "in": "header", "description": "deviceid of device android, ios or web", "type": "string", "required": true}, {"name": "body", "in": "body", "description": "Update User Profile", "schema": {"$ref": "#/definitions/UpdateUserCurrency"}}], "responses": {"200": {"description": "Update User Currency Response"}}}}}, "definitions": {"SignInAsGuestReq": {"required": ["deviceToken"], "properties": {"deviceToken": {"type": "string", "example": "48s7ad5s18d584de1dx5sdx15sx18w74d5d1xd8d5d145df450", "required": true}}}, "UpdateUserProfileReq": {"required": ["avatar", "name"], "properties": {"avatar": {"type": "string", "example": "FeralShadow", "required": true}, "name": {"type": "string", "example": "user0001", "required": true}, "country": {"type": "string", "example": "India", "required": true}}}, "SocialLoginAndRegisterRes": {"properties": {"status": {"type": "boolean", "example": "true"}, "statusCode": {"type": "number", "example": "200"}, "message": {"type": "string", "example": "SUCCESS"}, "data": {"type": "object", "example": {"accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6ImFkNjQxZDA2LThlMTItNGIyYy04YmQ3LTk0Y2VkMGM4NDg0ZiIsImFsZ29yaXRobSI6IkhTMjU2IiwiZXhwIjoxNzUwMDg1NjM5LCJpYXQiOjE3NDk3MjQxOTl9.T-Ttgxl0Tr8wbEbSEMlMJ1X3_hJQSIdyHCm2mBeWxUg"}}}}, "UpdateUserCurrency": {"required": ["type", "qty"], "properties": {"type": {"type": "string", "example": "coin || gem", "required": true}, "qty": {"type": "number", "example": 10, "required": true}}}}}