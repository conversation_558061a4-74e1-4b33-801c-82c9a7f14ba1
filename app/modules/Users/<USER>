import { CONSTANTS } from "../constants";

const eventCases = async (socket) => {
  try {
    socket.onAny(async (eventName, payload, ack) => {
    //   const {
    //     HIT,
    //     TEST,
    //     KNOCK,
    //     SIGNUP,
    //     SPREAD,
    //     DEFAULT,
    //     PICK_CARD,
    //     HEART_BEAT,
    //     THROW_CARD,
    //     SORT_CARDS,
    //     LEAVE_TABLE,
    //     ALERT_CONFIRMATION,
    //   } = CONSTANTS.EVENTS_NAME;

    //   if (typeof payload === "string") {
    //     payload = JSON.parse(payload);
    //   }

    
    });
  } catch (error) {
    console.log("eventCases Error : ", error);
  }
};

export { eventCases };