const _ = require("lodash");
const { DataTypes } = require("sequelize");
const { sequelizeConnection } = require("../../../configs/database");
const { SOCIAL_TYPES } = require("../../services/constant");

let appUserSchema = sequelizeConnection.define("user", {
  _id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  name: { type: DataTypes.STRING, allowNull: true },
  country: { type: DataTypes.STRING },
  email: { type: DataTypes.STRING },
  avatar: { type: DataTypes.STRING },
  deviceToken: { type: DataTypes.STRING },
  isGuest: { type: DataTypes.BOOLEAN, defaultValue: false },
  isDeleted: { type: DataTypes.BOOLEAN, defaultValue: false },
  status: { type: DataTypes.BOOLEAN, defaultValue: true },
  removeAds: { type: DataTypes.BOOLEAN, defaultValue: false },
  vipPass: { type: DataTypes.BOOLEAN, defaultValue: false },
  isProfileCompleted: { type: DataTypes.BOOLEAN, defaultValue: false },
  coins: { type: DataTypes.INTEGER },
  gems: { type: DataTypes.INTEGER },
});

/**************************
 AUTHENTICATION SCHEMA INITIALISATION
 **************************/

let UserAuthToken = sequelizeConnection.define("userAuth", {
  _id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  userId: { type: DataTypes.UUID },
  refreshToken: { type: DataTypes.TEXT },
  accessToken: { type: DataTypes.TEXT },
  deviceId: { type: DataTypes.STRING },
  ipAddress: { type: DataTypes.STRING },
  tokenExpiry: { type: DataTypes.DATE },
});

UserAuthToken.belongsTo(appUserSchema, {
  foreignKey: "userId",
});
appUserSchema.hasOne(UserAuthToken);

const SocialTypes = sequelizeConnection.define('socialTypes', {

    _id: { type: DataTypes.UUID, defaultValue: DataTypes.UUIDV4, primaryKey: true },
    socialId: { type: DataTypes.STRING },
    socialType: {
        type: DataTypes.ENUM(
            SOCIAL_TYPES.APPLE,
            SOCIAL_TYPES.GOOGLE
        ),
        allowNull: false,
    },
    userId: { type: DataTypes.UUID, references: { model: appUserSchema, key: '_id' }, allowNull: false },
    isActive: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: true },
    isDeleted: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
    deletedAt: { type: DataTypes.DATE }

}, { timestamps: true, freezeTableName: true })

appUserSchema.hasMany(SocialTypes, { foreignKey: "userId" });
SocialTypes.belongsTo(appUserSchema, { foreignKey: "userId" });

module.exports = {
  appUserSchema,
  UserAuthToken,
  SocialTypes
};
