const _ = require("lodash");
const i18n = require("i18n");
const Controller = require("../Base/Controller");
const Globals = require("../../../configs/Globals");
const { appUserSchema, SocialTypes } = require("./Schema");
const Form = require("../../services/Form");
const config = require("../../../configs/configs");
const { Op } = require("sequelize");
const { HTTP_CODE } = require("../../services/constant");
const Common = require("../../services/Common");

class UsersController extends Controller {
  constructor() {
    super();
  }

  async singInAsGuest() {
    try {
      const { deviceToken } = this.req.body;
      const { deviceid: deviceId } = this.req.headers;

      const findUserDeviceToken = await appUserSchema.findOne({
        where: {
          isDeleted: false,
          deviceToken,
        },
        raw: true,
        attributes: ["_id"],
      });

      const tokenPayload = {
        id: findUserDeviceToken?._id,
        deviceId,
      };

      if (!findUserDeviceToken) {
        const req = {
          ...this.req.body,
          coins: 20000,
          gems: 0,
          isGuest: true,
        };

        const guestUser = await appUserSchema.create(req, {
          returning: true,
          raw: true,
        });

        tokenPayload.id = guestUser._id;
      }

      let token = await new Globals().getUserToken(tokenPayload);

      return this.res.send({
        status: true,
        statusCode: HTTP_CODE.SUCCESS_CODE,
        message: i18n.__("SUCCESS"),
        data: {
          accessToken: token,
        },
      });
    } catch (error) {
      /********************************************************
        Manage Error logs and Response
        ********************************************************/
      console.error("error singInAsGuest()", error);
      return this.res.send({ status: 0, message: i18n.__("SERVER_ERROR") });
    }
  }

  async updateProfile() {
    try {
      const currentUser = this.req.currentUser._id;
      const updateData = {
        ...this.req.body,
        isProfileCompleted: true,
      };

      const [affectedCount, [updatedUser]] = await appUserSchema.update(
        updateData,
        {
          where: {
            _id: currentUser,
            isDeleted: false,
          },
          returning: true,
        }
      );

      if (!affectedCount) {
        return this.res.send({
          status: 0,
          message: i18n.__("USER_NOT_FOUND"),
        });
      }

      let response = {
        _id: updatedUser._id,
        name: updatedUser.name,
        country: updatedUser.country,
        email: updatedUser.email,
        avatar: updatedUser.avatar,
        isGuest: updatedUser.isGuest,
        status: updatedUser.status,
        removeAds: updatedUser.removeAds,
        vipPass: updatedUser.vipPass,
        isProfileCompleted: updatedUser.isProfileCompleted,
        coins: updatedUser.coins,
        gems: updatedUser.gems,
      };

      return this.res.send({
        status: true,
        statusCode: HTTP_CODE.SUCCESS_CODE,
        message: i18n.__("SUCCESS"),
        data: response,
      });
    } catch (error) {
      /********************************************************
        Manage Error logs and Response
        ********************************************************/
      console.error("error updateProfile()", error);
      return this.res.send({ status: 0, message: i18n.__("SERVER_ERROR") });
    }
  }

  async getProfile() {
    try {
      const currentUser = this.req.currentUser._id;

      const userProfile = await appUserSchema.findOne({
        where: {
          _id: currentUser,
          isDeleted: false,
        },
        raw: true,
        attributes: [
          "_id",
          "name",
          "country",
          "email",
          "avatar",
          "isGuest",
          "isProfileCompleted",
          "coins",
          "gems",
          "removeAds",
          "vipPass",
        ],
      });

      if (!userProfile) {
        return this.res.send({ status: 0, message: i18n.__("USER_NOT_FOUND") });
      }

      return this.res.send({
        status: true,
        statusCode: HTTP_CODE.SUCCESS_CODE,
        message: i18n.__("SUCCESS"),
        data: userProfile,
      });
    } catch (error) {
      /********************************************************
        Manage Error logs and Response
        ********************************************************/
      console.error("error getProfile()", error);
      return this.res.send({ status: 0, message: i18n.__("SERVER_ERROR") });
    }
  }

  async deleteProfile() {
    try {
      const currentUser = this.req.currentUser._id;

      const findUser = await appUserSchema.findOne({
        where: {
          _id: currentUser,
          isDeleted: false,
        },
        raw: true,
      });

      if (!findUser) {
        return this.res.send({
          status: 0,
          message: i18n.__("USER_NOT_FOUND"),
        });
      }

      let [affectedCount, [updatedData]] = await appUserSchema.update(
        {
          isDeleted: true,
        },
        {
          where: {
            _id: currentUser,
            isDeleted: false,
          },

          returning: true,
        }
      );

      if (!affectedCount) {
        return this.res.send({
          status: 0,
          message: i18n.__("USER_NOT_FOUND"),
        });
      }

      return this.res.send({
        status: true,
        statusCode: HTTP_CODE.SUCCESS_CODE,
        message: i18n.__("PROFILE_DELETED_SUCCESSFULLY"),
      });
    } catch (error) {
      /********************************************************
        Manage Error logs and Response
        ********************************************************/
      console.error("error deleteProfile()", error);
      return this.res.send({ status: 0, message: i18n.__("SERVER_ERROR") });
    }
  }

  async userRegisterAndLogin() {
    try {
      let { socialId, socialType, deviceToken, idToken } = this.req.body;
      let userData = await appUserSchema.findOne({
        where: {
          deviceToken: deviceToken,
          isDeleted: false,
        },
        raw: true,
      });

      if (userData) {
        const existingSocial = await SocialTypes.findOne({
          where: {
            socialId: socialId,
            socialType: socialType,
            userId: userData._id,
            isDeleted: false,
          },
          raw: true,
        });

        if (existingSocial) {
          const tokenPayload = {
            id: existingSocial?.userId,
          };
          let token = await new Globals().getUserToken(tokenPayload);
          return this.res.send({
            status: true,
            statusCode: HTTP_CODE.SUCCESS_CODE,
            message: i18n.__("SUCCESS"),
            data: {
              accessToken: token,
            },
          });
        } else {
          const newUser = await appUserSchema.create({
            deviceToken: deviceToken,
            isDeleted: false,
          });
          const socialData = await SocialTypes.create({
            socialId,
            socialType,
            userId: newUser?._id,
            isActive: true,
            isDeleted: false,
          });

          const tokenPayload = {
            id: socialData?.userId,
          };
          let token = await new Globals().getUserToken(tokenPayload);
          return this.res.send({
            status: true,
            statusCode: HTTP_CODE.SUCCESS_CODE,
            message: i18n.__("SUCCESS"),
            data: {
              accessToken: token,
            },
          });
        }
      } else {
        const newUser = await appUserSchema.create({
          deviceToken: deviceToken,
          isDeleted: false,
        });
        const socialData = await SocialTypes.create({
          socialId,
          socialType,
          userId: newUser?._id,
          isActive: true,
          isDeleted: false,
        });

        const tokenPayload = {
          id: socialData?.userId,
        };
        let token = await new Globals().getUserToken(tokenPayload);
        return this.res.send({
          status: true,
          statusCode: HTTP_CODE.SUCCESS_CODE,
          message: i18n.__("SUCCESS"),
          data: {
            accessToken: token,
          },
        });
      }
    } catch (error) {
      console.error("Error in userRegisterAndLogin:", error);
      return this.res.send({
        status: false,
        statusCode: HTTP_CODE.SERVER_ERROR_CODE,
        message: i18n.__("SERVER_ERROR"),
      });
    }
  }

  async addCurrency() {
    try {
      const currentUser = this.req.currentUser._id;
      const { type, qty } = this.req.body;

      const getUserCurrency = await appUserSchema.findOne({
        where: {
          isDeleted: false,
          _id: currentUser,
        },
        attributes: ["_id", "coins", "gems"],
        raw: true,
      });

      let updateFields = {};
      if (type == "coin") {
        updateFields.coins = getUserCurrency.coins + qty;
      } else if (type == "gem") {
        updateFields.gems = getUserCurrency.gems + qty;
      } else {
        return this.res.send({
          status: false,
          statusCode: HTTP_CODE.SERVER_ERROR_CODE,
          message: i18n.__("INVALID_CURRENCY_TYPE"),
        });
      }

      const [affectedCount] = await appUserSchema.update(updateFields, {
        where: {
          _id: currentUser,
          isDeleted: false,
        },
        returning: true,
      });

      if (!affectedCount) {
        return this.res.send({
          status: 0,
          message: i18n.__("USER_NOT_FOUND"),
        });
      }

      const updatedUser = await appUserSchema.findOne({
        where: { _id: currentUser, isDeleted: false },
        attributes: [
          "_id",
          "name",
          "country",
          "email",
          "avatar",
          "isGuest",
          "status",
          "removeAds",
          "vipPass",
          "isProfileCompleted",
          "coins",
          "gems",
        ],
      });

      if (!updatedUser) {
        return this.res.send({
          status: 0,
          message: i18n.__("USER_NOT_FOUND"),
        });
      }

      return this.res.send({
        status: true,
        statusCode: HTTP_CODE.SUCCESS_CODE,
        message: i18n.__("SUCCESS"),
        data: updatedUser,
      });
    } catch (error) {
      console.error("Error in userRegisterAndLogin:", error);
      return this.res.send({
        status: false,
        statusCode: HTTP_CODE.SERVER_ERROR_CODE,
        message: i18n.__("SERVER_ERROR"),
      });
    }
  }
}

module.exports = UsersController;
