/****************************
 Validators
 ****************************/
const _ = require("lodash");
const Joi = require("joi");
const i18n = require("i18n");

class Validators {
  static loginValidate() {
    return async (req, res, next) => {
      try {
        req.schema = Joi.object().keys({
          deviceToken: Joi.string()
            .required()
            .error(new Error(i18n.__("JOI_VALID_DEVICE_TOKEN"))),
        });
        next();
      } catch (error) {
        throw new Error(error);
      }
    };
  }

  static profileUpdateValidate() {
    return async (req, res, next) => {
      try {
        req.schema = Joi.object().keys({
          avatar: Joi.string()
            .required()
            .error(new Error(i18n.__("JOI_VALID_AVATAR"))),
          name: Joi.string()
            .required()
            .error(new Error(i18n.__("JOI_VALID_NAME"))),
          country: Joi.string().error(new Error(i18n.__("JOI_VALID_COUNTRY"))),
        });
        next();
      } catch (error) {
        throw new Error(error);
      }
    };
  }

  static validateSocailUsers() {
    return async (req, res, next) => {
      try {
        req.schema = Joi.object().keys({
          socialId: Joi.string()
            .required()
            .error(new Error(i18n.__("JOI_FAIL_SOCIAL_ID"))),
          socialType: Joi.string()
            .required()
            .error(new Error(i18n.__("JOI_FAIL_SOCIAL_TYPE"))),
          deviceToken: Joi.string()
            .required()
            .error(new Error(i18n.__("JOI_FAIL_DEVICE_TOKEN"))),
          idToken: Joi.string()
            .required()
            .error(new Error(i18n.__("JOI_FAIL_ID_TOKEN"))),
        });
        next();
      } catch (error) {
        throw new Error(error);
      }
    };
  }
  static addCurrency() {
    return async (req, res, next) => {
      try {
        req.schema = Joi.object().keys({
          type: Joi.string()
            .valid("coin", "gem") // Only allow these two values
            .required()
            .error(new Error(i18n.__("JOI_VALID_CURRENCY_TYPE"))),
          qty: Joi.number()
            .required()
            .error(new Error(i18n.__("JOI_VALID_CURRENCY_QTY"))),
        });
        next();
      } catch (error) {
        throw new Error(error);
      }
    };
  }
}

module.exports = Validators;
