module.exports = (app, express) => {
  const router = express.Router();
  const Globals = require("../../../configs/Globals");
  const UsersController = require("./Controller");
  const Middleware = require("../../services/middleware");
  const Validators = require("./Validator");
  const config = require("../../../configs/configs");

  /********************************************************
        User Register
    ********************************************************/

  router.post(
    "/join-as-guest",
    Globals.isHeaderAuthorized(),
    Validators.loginValidate(),
    Middleware.validateBodyApp,
    (req, res) => {
      const userObj = new UsersController().boot(req, res);
      return userObj.singInAsGuest();
    }
  );

  router.put(
    "/profile",
    Globals.isUserAuthorized(),
    Validators.profileUpdateValidate(),
    Middleware.validateBodyApp,
    (req, res) => {
      const userObj = new UsersController().boot(req, res);
      return userObj.updateProfile();
    }
  );

  router.get("/profile", Globals.isUserAuthorized(), (req, res) => {
    const userObj = new UsersController().boot(req, res);
    return userObj.getProfile();
  });

  router.delete("/profile", Globals.isUserAuthorized(), (req, res) => {
    const userObj = new UsersController().boot(req, res);
    return userObj.deleteProfile();
  });

  router.post(
    "/user/socialLoginAndRegister",
    Validators.validateSocailUsers(),
    Middleware.validateBodyApp,
    (req, res) => {
      const userObj = new UsersController().boot(req, res);
      return userObj.userRegisterAndLogin();
    }
  );

  router.put(
    "/add-currency",
    Globals.isUserAuthorized(),
    Validators.addCurrency(),
    Middleware.validateBodyApp,
    (req, res) => {
      const userObj = new UsersController().boot(req, res);
      return userObj.addCurrency();
    }
  );
  app.use(config.baseApiUrl, router);
};
