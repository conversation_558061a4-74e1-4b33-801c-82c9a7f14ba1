const { generate } = require("randomstring");

const dummyTable = async () => {
    return {
        tableId: generate(12),
        users: [],
        isGameStart: false,
        isGameEnd: false,
        currentNumber: null,
        calledNumbers: [],
        notCalledNumbers:[],
        gameStartTime: Date.now(),
    };
};

const dummyPrivateTable = async (payload) => {
    return {
        tableId: payload.privateTableCode,
        users: [],
        isGameStart: false,
        isGameEnd: false,
        currentNumber: null,
        calledNumbers: [],
        notCalledNumbers:[],
        gameStartTime: Date.now(),
    };
};

module.exports = { dummyTable, dummyPrivateTable };