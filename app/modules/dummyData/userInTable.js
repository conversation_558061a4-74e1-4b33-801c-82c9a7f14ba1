
const { generate } = require("randomstring");
const {generateValidTicket} = require('../../services/generateTicketAlgorithm')
const dummyUserInTable = async (user, tableId, seatIndex, socketId) => {
var tickets = [];
for (let i = 0; i < 4; i++) {
    
  const ticket = generateValidTicket();
  const data = {
    "ticketId": generate(12),
    "ticket": ticket,
    "claimedNumbers":[]
  }
  tickets.push(data);
}

    return {
        userId:user.userId,
        tableId,
        seatIndex,
        socketId,
        tickets: tickets,
        claims: {
          firstRow: false,
          secondRow: false,
          earlyFive: false,
          thirdRow: false,
          fullHouse: false,
        },
    };
};    

const dummyUserInPrivateTable = async (user, tableId, seatIndex, socketId, noOfTickets) =>{
  var tickets = [];
for (let i = 0; i < noOfTickets; i++) {
    
  const ticket = generateValidTicket();
  const data = {
    "ticketId": generate(12),
    "ticket": ticket,
    "claimedNumbers":[]
  }
  tickets.push(data);
}

    return {
        userId:user.userId,
        tableId,
        seatIndex,
        socketId,
        tickets: tickets,
        claims: {
          firstRow: false,
          secondRow: false,
          earlyFive: false,
          thirdRow: false,
          fullHouse: false,
        },
    };
}

module.exports = { dummyUserInTable, dummyUserInPrivateTable };