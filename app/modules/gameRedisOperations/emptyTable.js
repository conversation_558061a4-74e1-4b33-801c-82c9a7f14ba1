const {
  setData,
  removeEmptyTable,
  find<PERSON><PERSON><PERSON>,
  find<PERSON>ey,
} = require("../../services/redisOperations");

const { logger } = require("../../services/logger");

const { REDIS_COLLECTION } = require("../../services/constant");

const generateEmptyTable = async (key) => {
  return `${REDIS_COLLECTION.EMPTY_TABLES}:${key}`;
};

const generatePrivateTable = async (key) => {
  return `${REDIS_COLLECTION.PRIVATE_EMPTY_TABLES}:${key}`;
};

const setEmptyTable = async (tableId) => {
  logger.log("setEmptyTable", { tableId });
  const key = await generateEmptyTable(tableId);
  await setData(key, tableId);
};

const getEmptyTable = async (tableId) => {
  logger.log("getEmptyTable", { tableId });
  const key = await generateEmptyTable("*");

  const emptyTableData = await find<PERSON>ey<PERSON>(key);

  logger.log("getEmptyTable Return : ", { emptyTableData });

  return emptyTableData;
};

const deleteEmptyTable = async (tableId) => {
  logger.log("deleteEmptyTable", { tableId });

  const key = await generateEmptyTable(tableId);
  await removeEmptyTable(key);
};

const getPrivateTable = async (tableId) => {
  logger.log("getPrivate Table ", { tableId });

  const key = await generatePrivateTable(tableId);

  const emptyTableData = await findKey(key);

  logger.log("getEmptyTable Return : ", { emptyTableData });

  return emptyTableData;
};

const setPrivateEmptyTable = async (tableId) => {
  logger.log("setPrivateEmptyTable", { tableId });
  const key = await generatePrivateTable(tableId);
  await setData(key, tableId);
};

module.exports = {
  setEmptyTable,
  getEmptyTable,
  deleteEmptyTable,
  getPrivateTable,
  setPrivateEmptyTable,
};
