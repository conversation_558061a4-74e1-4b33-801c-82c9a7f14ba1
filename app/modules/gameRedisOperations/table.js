const {
  setData,
  getData,
  deleteData,
} = require("../../services/redisOperations");
const { REDIS_COLLECTION } = require("../../services/constant");
const { logger } = require("../../services/logger");

const generateTableKey = async (key) => `${REDIS_COLLECTION.TABLES}:${key}`;
const generatePrivateTableKey = async (key) => `${REDIS_COLLECTION.PRIVATE_TABLES}:${key}`

const setTable = async (tableId, tableData) => {

  logger.log("setTable", { tableId, tableData });

  await setData(await generateTableKey(tableId), tableData);
};

const getTable = async (tableId) => {

  logger.log("getTable", { tableId });

  const tableData = await getData(await generateTableKey(tableId));

  logger.log("getTable Return : ", { tableData });

  return tableData
    ? tableData
    : Promise.reject(new Error("Table Not Found !!!"));
};

const deleteTable = async (tableId) => {
  
  logger.log("deleteTable", { tableId });

  await deleteData(await generateTableKey(tableId));
};

const setPrivateTable = async (tableId, tableData) => {

  logger.log("setPrivateTable", { tableId, tableData });

  await setData(await generatePrivateTableKey(tableId), tableData);
};

module.exports = { setTable, getTable, deleteTable, setPrivateTable };
