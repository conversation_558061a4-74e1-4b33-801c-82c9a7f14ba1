const { setData, getData, deleteData } = require("../../services/redisOperations");
const { REDIS_COLLECTION } = require('../../services/constant')
const { logger } = require("../../services/logger");


const generateUserKey = async (key) => `${REDIS_COLLECTION.USERS}:${key}`;
const setUser = async (userId, userData) => {
  
  logger.log("setUser", { userId, userData });

  await setData(await generateUserKey(userId), userData);

};

const getUser = async (userId) => {

  logger.log("getUser", { userId });

  const userData = await getData(await generateUserKey(userId));

  logger.log("getUser Return : ", { userData });

  return userData ? userData : Promise.reject(new Error("User Not Found !!!"));

};

const deleteUser = async (userId) => {

  logger.log("deleteUser", { userId });

  await deleteData(await generateUserKey(userId));

};

module.exports = {
  setUser,
  getUser,
  deleteUser
};
