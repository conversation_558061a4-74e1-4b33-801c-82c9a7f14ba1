const { setData, getData, deleteData } = require("../../services/redisOperations");
const { REDIS_COLLECTION } = require('../../services/constant')
const { logger } = require("../../services/logger");

const generateUserInTableKey = async (tableId, userId) =>
  `${REDIS_COLLECTION.USER_IN_TABLE}:${tableId}:${userId}`;

const generateUserInPrivateTableKey = async (tableId, userId) =>
`${REDIS_COLLECTION.USER_IN_PRIVATE_TABLE}:${tableId}:${userId}`;

const setUserInTable = async (tableId, userId, userInTableData) => {
  logger.log("setUserInTable", { tableId, userId, userInTableData });

  await setData(await generateUserInTableKey(tableId, userId), userInTableData);

};

const getUserInTable = async (tableId, userId) => {

  logger.log("getUserInTable", { tableId, userId });

  const userInTableData = await getData(await generateUserInTableKey(tableId, userId));

  logger.log("getUserInTable Return : ", { userInTableData });

  return userInTableData ? userInTableData : Promise.reject(new Error("User In Table Not Found !!!"));

};

const deleteUserInTable = async (tableId, userId) => {

  logger.log("deleteUserInTable", { tableId, userId });

  await deleteData(await generateUserInTableKey(tableId, userId));

};

const setUserInPrivateTable = async (tableId, userId, userInTableData) => {
  logger.log("setUserInPrivateTable", { tableId, userId, userInTableData });

  await setData(await generateUserInPrivateTableKey(tableId, userId), userInTableData);

};

module.exports = {
  setUserInTable,
  getUserInTable,
  deleteUserInTable,
  setUserInPrivateTable
};
