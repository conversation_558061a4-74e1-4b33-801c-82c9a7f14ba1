const joi = require('joi');

const validatePrivateTable = async (data) => {

    return joi.object({

        isPrivate: joi.boolean().required(),
        noOfTickets: joi.number().min(1).max(4).required(),
        privateTableCode: joi.when('isPrivate', {
            is: true,
            then: joi.number().min(10000).max(99999).required(),
            otherwise: joi.forbidden()
          })
    }).validate(data)?.error?.details[0]?.message;

};

module.exports= { validatePrivateTable };