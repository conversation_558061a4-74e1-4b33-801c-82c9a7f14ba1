/****************************
 Common services
 ****************************/
const _ = require('lodash');
const bcrypt = require('bcrypt');
const Config = require('../../configs/configs');
const { Op } = require('sequelize');
const path = require('path');
const fs = require('fs');
const { S3Client } = require('@aws-sdk/client-s3');
const { OAuth2Client } = require("google-auth-library");
const jwksClient = require("jwks-rsa");
const jwt = require('jsonwebtoken');

const googleClient = new OAuth2Client(Config.GOOGLE_CLIENT_ID);
const appleJwksClient = jwksClient({ jwksUri: Config.APPLE_JWKS_URL });

const s3 = new S3Client({
  region: Config.s3Region,
  credentials: {
    accessKeyId: Config.s3AccessKeyId,
    secretAccessKey: Config.s3SecretAccessKey,
  },
});

const s3bucketName = Config.s3Bucket;


class Common {
  /********************************************************
   @Purpose Handle Error Response
   @Parameter
   {
      res, status, statusCode, message
   }
   @Return JSON String
   ********************************************************/
  handleReject(res, status, statusCode, message, readOnlyData) {
    let data = {
      status,
      statusCode,
      message,
    };
    if (readOnlyData) {
      data.readOnlyData = readOnlyData;
    }
    return res.status(statusCode).send(data);
  }


  async googleAuth(idToken) {

    try {

      if (!Config.GOOGLE_CLIENT_ID) { throw new Error("Google Client Id Not Provided"); };

      const ticket = await googleClient.verifyIdToken({ idToken, audience: Config.GOOGLE_CLIENT_ID });

      const payload = ticket?.getPayload();

      return (payload && payload?.email_verified) ? true : false;

    } catch (error) {
      console.log("googleAuth Error : ", error);
      return false;
    };
  };

  getApplePublicKey(kid) {

    return new Promise((resolve, reject) => {

      appleJwksClient.getSigningKey(kid, (err, key) => { return err ? reject(err) : resolve(key?.getPublicKey()); });

    });

  };

  async appleAuth(idToken) {

    try {

      if (!Config.APPLE_JWKS_URL) { throw new Error("Apple Social Details Missing."); };

      const decodedHeader = jwt.decode(idToken, { complete: true });

      if (!decodedHeader) { throw new Error("JWT Decode Fail"); };

      const { kid } = decodedHeader.header;

      const publicKey = await this.getApplePublicKey(kid);

      if (!publicKey) { throw new Error("Fail To Retrive Public Key"); };

      const verifiedPayload = jwt.verify(idToken, publicKey, { algorithms: ["RS256"] });

      if (!verifiedPayload) { throw new Error("Fail To Verify Token"); };

      return (verifiedPayload && verifiedPayload?.email_verified) ? true : false;

    } catch (error) {
      console.log("appleAuth Error : ", error);
      return false;
    };
  };

}

module.exports = new Common();