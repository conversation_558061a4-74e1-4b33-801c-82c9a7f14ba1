exports.HTTP_CODE = {
  SUCCESS: true,
  FAILED: false,
  SUCCESS_CODE: 200,
  SERVER_ERROR_CODE: 500,
  NOT_FOUND_CODE: 404,
  BAD_REQUEST_CODE: 400,
  RESOURCE_CREATED_CODE: 201,
  UNAUTHORIZED_CODE: 401,
  PROCESSING_CODE: 202,
  CONFLICT_CODE: 409,
  TOO_MANY_REQUEST_CODE: 429,
  PAYMENT_REQUIRED_CODE: 402,
  UNPROCESSABLE_ENTITY: 422,
  UNSUPPORTED_MEDIA_TYPE: 415,
  USE_PROXY: 305,
  PROCESSING: 102,
  PROXY_AUTHENTICATION_REQUIRED: 407,
  REQUEST_HEADER_FIELDS_TOO_LARGE: 431,
  REQUEST_TIMEOUT: 408,
  REQUEST_TOO_LONG: 413,
  REQUEST_URI_TOO_LONG: 414,
  REQUESTED_RANGE_NOT_SATISFIABLE: 416,
  RESET_CONTENT: 205,
  SEE_OTHER: 303,
  SERVICE_UNAVAILABLE: 503,
  SWITCHING_PROTOCOLS: 101,
  TEMPORARY_REDIRECT: 307,
  LENGTH_REQUIRED: 411,
  LOCKED: 423,
  METHOD_FAILURE: 420,
  METHOD_NOT_ALLOWED: 405,
  MOVED_PERMANENTLY: 301,
  MOVED_TEMPORARILY: 302,
  MULTI_STATUS: 207,
  MULTIPLE_CHOICES: 300,
  NETWORK_AUTHENTICATION_REQUIRED: 511,
};

exports.DATABASE_VALIDATION = {
  UNIQUE_CONSTRAINTS_VIOLATION: '23505',
};

exports.WHITELISTED_URLS = [
  'https://universal-angular-node.devpress.net',
  'https://universal-reactjs-dev.devpress.net',
  'https://universal-angular-dev.devpress.net',
  'http://localhost:3000/'
];

exports.SOCIAL_TYPES = {
  APPLE: 'apple',
  GOOGLE: 'google',
};

exports.COMMON = {
  LIVE_ENVIRONMENT: "live",
  TURN_MISSED: "Turn Missed",
  DISCONNECTED: "Disconneted",
  UNKNOWN_EVENT: "Unknown Event",
  ERROR_OCCURRED: "Error Occurred",
  MAX_KONCK_LOCK_COUNT: 5,
};

exports.EVENTS_NAME = {
  TEST: "TEST",
  DEFAULT: "DEFAULT",
  ALERT_CONFIRMATION: "ALERT_CONFIRMATION",
  ERROR_MESSAGE: "ERROR_MESSAGE",
  CREATE_TABLE: "CREATE_TABLE",
  JOIN_TABLE: "JOIN_TABLE",
  GAME_START: "GAME_START",
  HEART_BEAT: "HEART_BEAT",
  ERROR_POPUP: "ERROR_POPUP",
  GENERATE_PRIVATE_GAME_CODE: "GENERATE_PRIVATE_GAME_CODE"
};

exports.REDIS_COLLECTION = {
  LOCK: "Lock",
  USERS: "Users",
  TABLES: "Tables",
  WINNING: "Winning",
  MATCH_MAKING: "Match_Making",
  EMPTY_TABLES: "Empty_Tables",
  USER_IN_TABLE: "User_In_Table",
  PRIVATE_TABLES: "PRIVATE_TABLES",
  PRIVATE_EMPTY_TABLES:"PRIVATE_EMPTY_TABLES",
  USER_IN_PRIVATE_TABLE: "USER_IN_PRIVATE_TABLE"
}
