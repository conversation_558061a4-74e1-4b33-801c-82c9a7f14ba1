const _ = require('lodash');
const { sequelizeConnection } = require('../../configs/database');

class Database {
  /********************************************************
   @Purpose upsert based on non unique field
   @Parameter
   {
      
   }
   @Return updated value 
   ********************************************************/
  async findOneAndUpdate(model, condition, dataToUpdate) {
    const table = sequelizeConnection.model(model);
    const data = await table.findOne({ where: condition });
    if (_.isEmpty(data)) {
      return await table.create(dataToUpdate);
    } else {
      return await table.update(dataToUpdate, { where: condition, returning: true });
    }
  }
}

module.exports = new Database();
