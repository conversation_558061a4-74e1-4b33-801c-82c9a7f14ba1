const crypto = require('crypto'); 
const Config = require("../../configs/configs");

// Key should be 32 bytes for AES-256
const ENCRYPTION_KEY = Buffer.from(Config.ENCRYPTION_KEY, 'hex');

const IV = crypto.randomBytes(16);

class Encryption {
  /********************************************************
  @Purpose Encrypt Method
  @Parameter
      {
          "text" : "encrytion data"
    }
  @Return String
  ********************************************************/
  encrypt = (text) => {
    const cipher = crypto.createCipheriv(
      "aes-256-cbc",
      Buffer.from(ENCRYPTION_KEY),
      IV
    );
    let encrypted = cipher.update(text);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return `ENC_${encrypted.toString("hex")}`; // Prefix to identify encrypted data
  };

  /********************************************************
  @Purpose Decryption Method
  @Parameter
      {
          "text" : "encrytion data"
    }
  @Return String
  ********************************************************/
  decrypt = (text) => {
    const encryptedText = Buffer.from(text.replace("ENC_", ""), "hex");
    const decipher = crypto.createDecipheriv(
      "aes-256-cbc",
      Buffer.from(ENCRYPTION_KEY),
      IV
    );
    let decrypted = decipher.update(encryptedText);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    return decrypted.toString();
  };
}

module.exports = new Encryption();
