function createHousieArray() {
    const arr = [];
    for (let i = 1; i <= 90; i++) {
      arr.push(i);
    }
    return arr;
  }
  
  // Step 2: Shuffle the array using Fisher<PERSON>Yates algorithm
  function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1)); // Random index between 0 and i
      // Swap array[i] and array[j]
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }
  
  // Example usage:
  const housieNumbers = createHousieArray();
  const shuffledHousieNumbers = shuffleArray(housieNumbers);

  module.exports = {
    shuffledHousieNumbers
  }