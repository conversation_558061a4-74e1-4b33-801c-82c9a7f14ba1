const { randomInt } = require('crypto');

// Constants
const NUM_ROWS = 3;
const NUM_COLUMNS = 9;
const NUMBERS_PER_ROW = 5;
const TOTAL_NUMBERS = NUM_ROWS * NUMBERS_PER_ROW; // 15

// Pre-calculated column ranges
const COLUMN_RANGES = Array.from({ length: NUM_COLUMNS }, (_, i) => ({
    min: i === 0 ? 1 : i * 10,
    max: i === NUM_COLUMNS - 1 ? 90 : (i + 1) * 10 - 1
}));

// Pre-defined subgrid column groups
const SUBGRIDS = [
    { name: "Left (1-3)", cols: [0, 1, 2] },
    { name: "Middle (4-6)", cols: [3, 4, 5] },
    { name: "Right (7-9)", cols: [6, 7, 8] }
];

// Fisher-<PERSON> shuffle with crypto randomness
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = randomInt(0, i + 1);
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Generate a random number within a range without duplicates
function getUniqueRandomNumber(min, max, usedNumbers) {
    let num;
    do {
        num = randomInt(min, max + 1);
    } while (usedNumbers.has(num));
    return num;
}

// Count numbers in a row (optimized)
function countNumbersInRow(row) {
    let count = 0;
    for (const num of row) {
        if (num > 0) count++;
    }
    return count;
}

// Generate a valid Housie ticket with all constraints
function generateValidTicket() {
    while (true) {
        const grid = Array.from({ length: NUM_ROWS }, () => Array(NUM_COLUMNS).fill(0));
        const usedNumbers = new Set();
        const columnCounts = Array(NUM_COLUMNS).fill(0);
        
        // Step 1: Assign one number to each column first
        const columns = [...Array(NUM_COLUMNS).keys()];
        shuffleArray(columns);
        
        for (const col of columns) {
            const { min, max } = COLUMN_RANGES[col];
            let row;
            
            // Find available rows more efficiently
            const availableRows = [];
            for (let r = 0; r < NUM_ROWS; r++) {
                if (countNumbersInRow(grid[r]) < NUMBERS_PER_ROW) {
                    availableRows.push(r);
                }
            }
            
            row = availableRows[randomInt(0, availableRows.length)];
            
            const num = getUniqueRandomNumber(min, max, usedNumbers);
            grid[row][col] = num;
            usedNumbers.add(num);
            columnCounts[col]++;
        }
        
        // Step 2: Fill remaining numbers
        let remainingNumbers = TOTAL_NUMBERS - NUM_COLUMNS;
        
        while (remainingNumbers > 0) {
            const col = randomInt(0, NUM_COLUMNS);
            const { min, max } = COLUMN_RANGES[col];
            
            // Find available rows for this column
            const availableRows = [];
            for (let row = 0; row < NUM_ROWS; row++) {
                if (grid[row][col] === 0 && countNumbersInRow(grid[row]) < NUMBERS_PER_ROW) {
                    availableRows.push(row);
                }
            }
            
            if (availableRows.length === 0) continue;
            
            const row = availableRows[randomInt(0, availableRows.length)];
            const num = getUniqueRandomNumber(min, max, usedNumbers);
            
            grid[row][col] = num;
            usedNumbers.add(num);
            columnCounts[col]++;
            remainingNumbers--;
        }
        
        // Step 3: Verify all constraints
        if (validateTicket(grid).isValid) {
            return grid;
        }
    }
}

// Optimized validation
function validateTicket(grid) {
    const errors = [];
    const allNumbers = [];
    const columnNumbers = Array.from({ length: NUM_COLUMNS }, () => []);
    
    // Check rows and collect column numbers
    for (let row = 0; row < NUM_ROWS; row++) {
        const rowNumbers = [];
        for (let col = 0; col < NUM_COLUMNS; col++) {
            const num = grid[row][col];
            if (num > 0) {
                rowNumbers.push(num);
                columnNumbers[col].push(num);
                allNumbers.push(num);
            }
        }
        if (rowNumbers.length !== NUMBERS_PER_ROW) {
            errors.push(`Row ${row + 1} has ${rowNumbers.length} numbers (expected ${NUMBERS_PER_ROW})`);
        }
    }
    
    // Check columns
    for (let col = 0; col < NUM_COLUMNS; col++) {
        const { min, max } = COLUMN_RANGES[col];
        const nums = columnNumbers[col];
        
        if (nums.length === 0) {
            errors.push(`Column ${col + 1} has no numbers`);
            continue;
        }
        
        for (const num of nums) {
            if (num < min || num > max) {
                errors.push(`Number ${num} in column ${col + 1} is outside range ${min}-${max}`);
            }
        }
        
        // Check sorting
        for (let i = 1; i < nums.length; i++) {
            if (nums[i] < nums[i - 1]) {
                errors.push(`Column ${col + 1} numbers are not sorted`);
                break;
            }
        }
    }
    
    // Check duplicates
    if (new Set(allNumbers).size !== allNumbers.length) {
        errors.push("Ticket contains duplicate numbers");
    }
    
    // Check 3x3 subgrid constraints
    for (const { name, cols } of SUBGRIDS) {
        let count = 0;
        for (let row = 0; row < NUM_ROWS; row++) {
            for (const col of cols) {
                if (grid[row][col] > 0) count++;
            }
        }
        if (count !== 5) {
            errors.push(`${name} subgrid has ${count} numbers (expected 5)`);
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

// Optimized test function
function testTicketGeneration(count = 10) {
    console.log(`Generating ${count} tickets...\n`);
    const startTime = Date.now();
    let validCount = 0;
    
    for (let i = 0; i < count; i++) {
        const ticket = generateValidTicket();
        const validation = validateTicket(ticket);
        
        if (validation.isValid) {
            validCount++;
            console.log(`Ticket ${i + 1}: VALID`);
        } else {
            console.log(`Ticket ${i + 1}: INVALID`);
            console.log("Errors:", validation.errors);
        }
        
        // Print the ticket
        ticket.forEach((row, idx) => {
            console.log(`Row ${idx + 1}:`, row.map(n => n > 0 ? n.toString().padStart(2, '0') : '  ').join(' '));
        });
        console.log('\n');
    }
    
    const elapsedTime = (Date.now() - startTime) / 1000;
    console.log(`Successfully generated ${validCount}/${count} valid tickets in ${elapsedTime.toFixed(2)} seconds`);
}

module.exports = {
    generateValidTicket,
    validateTicket,
    testTicketGeneration
};