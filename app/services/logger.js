const winston = require("winston");
const config = require("../../configs/configs");

const winstonLogger = winston.createLogger({
  format: winston.format.combine(
    winston.format.simple(),
    winston.format.printf((info) => `${info.level} : ${info.message}\n.`)
  ),
  transports: [new winston.transports.Console()],
});

const indianTime = () => {
  const utcDate = new Date();

  const istDate = new Intl.DateTimeFormat("en-IN", {
    timeZone: "Asia/Kolkata",
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
    second: "numeric",
  }).format(utcDate);

  return `${istDate} ${utcDate.getMilliseconds().toString().padStart(3, "0")}`;
};

const log = (path, data, strict) => {
  if (config.LOGGER_STATUS || strict) {
    winstonLogger.info(
      `${indianTime()} >> Path : ${path} >> Data : ${JSON.stringify(data)}`
    );
  }
};

const errorLog = (path, error) => {
  winstonLogger.error(`${indianTime()} >> Path : ${path}${error.stack}`);
};

const logger = { log, errorLog };

module.exports = { logger };