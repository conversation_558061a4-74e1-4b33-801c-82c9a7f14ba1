const { default: Redlock } = require("redlock"); // ✅ Correct way to import Redlock in CommonJS
const Redis = require("ioredis");
const config = require("../../configs/configs");
const { logger } = require('./logger')

let redLock;

const redlockConnection = async () => {
  try {
    const redisOptions = {
      host: config.REDIS_HOST,
      port: config.REDIS_PORT,
     // password: config.REDIS_PASSWORD,
      db: config.REDIS_DATABASE_NUMBER,
    };

    const redisClient = new Redis(redisOptions);

    redLock = new Redlock([redisClient], {
      driftFactor: 0.01,
      retryCount: -1,
      retryDelay: 25,
      retryJitter: 20,
    });

    redLock.on("error", (error) => {
      logger.log("RedLock Error : ", error);
    });

    redLock.on("lockError", (error) => {
      logger.log("RedLock Lock Error : ", error);
    });

    redLock.on("unlockError", (error) => {
      logger.log("RedLock Unlock Error : ", error);
    });

    console.log("redLock Connected !!!");
  } catch (error) {
    console.log("redlockConnection Error : ", error);
  }
};

const applyLock = async (path, lockId, delay) => {
  try {
    logger.log("applyLock", { path, lockId, delay });

    const lock = await redLock.acquire([lockId], delay * 1000);
    return lock;
  } catch (error) {
    logger.log("applyLock Error : ", error);
  }
};

module.exports = {
  redlockConnection,
  applyLock,
};
