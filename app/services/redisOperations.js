const { redisClient } = require("../../configs/RedisConnection");
const { logger } = require('./logger')

const setData = async (key, data) => {
  try {
    if (!redisClient || !redisClient.isReady) {
      throw new Error("Redis client not ready");
    }
    logger.log("redis setData : ", { key, data });
    await redisClient.set(key, JSON.stringify(data));
    return true;
  } catch (error) {
    logger.log("redis setData Error : ", error);
    return false;
  }
};

const getData = async (key) => {
  try {
    if (!redisClient || !redisClient.isReady) {
      throw new Error("Redis client not ready");
    }
    logger.log("redis getData : ", { key });
    const data = await redisClient.get(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    logger.log("redis getData Error : ", error);
    return null;
  }
};

const deleteData = async (key) => {
  try {
    if (!redisClient || !redisClient.isReady) {
      throw new Error("Redis client not ready");
    }
    logger.log("redis deleteData : ", { key });
    await redisClient.del(key);
    return true;
  } catch (error) {
    logger.log("redis deleteData Error : ", error);
    return false;
  }
};

const addToArray = async (key, data) => {
  try {
    if (!redisClient || !redisClient.isReady) {
      throw new Error("Redis client not ready");
    }
    logger.log("redis addToArray : ", { key, data });
    await redisClient.lPush(key, data);
    return true;
  } catch (error) {
    logger.log("redis addToArray Error : ", error);
    return false;
  }
};

const getArray = async (key) => {
  try {
    if (!redisClient || !redisClient.isReady) {
      throw new Error("Redis client not ready");
    }
    logger.log("redis getArray : ", {key});
    const data = await redisClient.lRange(key, 0, -1);
    return data;
  } catch (error) {
    logger.log("redis getArray Error : ", error);
    return null;
  }
};

const removeFromArray = async (key, value) => {
  try {
    if (!redisClient || !redisClient.isReady) {
      throw new Error("Redis client not ready");
    }
    logger.log("redis removeFromArray Error : ", { key, value });
    await redisClient.lRem(key, 1, value);
    return true;
  } catch (error) {
    logger.log("redis removeFromArray Error : ", error);
    return false;
  }
};

const removeEmptyTable = async (key) => {
  try {
    if (!redisClient || !redisClient.isReady) {
      throw new Error("Redis client not ready");
    }
    logger.log("redis removeEmptyTable : ", { key });
    await redisClient.del(key);
    return true;
  } catch (error) {
    logger.log("redis removeEmptyTable Error : ", error);
    return false;
  }
};

const findKeys = async (key) => {
  try {
    if (!redisClient || !redisClient.isReady) {
      throw new Error("Redis client not ready");
    }
    logger.log("redis removeEmptyTable : ", { key });
    return await redisClient.keys(key);
   
  } catch (error) {
    logger.log("redis removeEmptyTable Error : ", error);
    return false;
  }
};

const findKey = async (key) => {
  try {
    if (!redisClient || !redisClient.isReady) {
      throw new Error("Redis client not ready");
    }
    logger.log("redis findKey : ", { key });
    console.log("89898989898989998989898998",key);
    const result = await redisClient.exists(key);
    console.log("PPPPPPPPPPPPPPPP",result);
    return result;
   
  } catch (error) {
    logger.log("redis findKey Error : ", error);
    return false;
  }
};

module.exports = {
  findKeys,
  setData,
  getData,
  deleteData,
  addToArray,
  getArray,
  removeFromArray,
  removeEmptyTable,
  findKey
};
