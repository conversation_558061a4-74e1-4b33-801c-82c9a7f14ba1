/****************************
 SECURITY TOKEN HANDLING
 ****************************/
const _ = require('lodash');
const Moment = require('moment');
let jwt = require('jsonwebtoken');

const config = require('./configs');
const CommonService = require('../app/services/Common');
const { HTTP_CODE } = require('../app/services/constant');
const database = require('../app/services/database');
const { Op } = require('sequelize');
const moment = require("moment");
const {UserAuthToken, appUserSchema } = require('../app/modules/Users/<USER>')
const i18n = require("i18n");
class Globals {
  generateToken(id) {
    return new Promise(async (resolve, reject) => {
      try {
        let token = jwt.sign(
          {
            id: id,
            algorithm: 'HS256',
            exp: Math.floor(Date.now() / 1000) + parseInt(config.tokenExpiry),
          },
          config.securityToken
        );

        return resolve(token);
      } catch (err) {
        console.error('Error generateToken', err);
        return reject({ message: err, status: 0 });
      }
    });
  }


  static isHeaderAuthorized() {
    return async (req, res, next) => {
      try {
        /********************************************************
          Get Device Id from header and valid it
         ********************************************************/
        let deviceId = req.headers.deviceid;
        if (!deviceId) {
          return res.status(401).json({ status: 0, message: i18n.__("REQUIRED_DEVICE_ID") })
        }
        if (deviceId == 'ios' || deviceId == 'android' || deviceId == 'web') {
          next();
        } else {
          return res.status(401).json({ status: 0, message: i18n.__("WRONG_DEVICE_ID") })
        }
      } catch (err) {
        console.error('Token authentication', err);
        return res.send({ status: 0, message: err });
      }
    };
  }

  static isUserAuthorized() {
    return async (req, res, next) => {
      try {
        /********************************************************
          Get auth token from header
         ********************************************************/
        const token = req.headers.authorization;
        if (!token) {
          return res.status(401).json({ status: 0, message: i18n.__("TOKEN_WITH_API") })
        }

        /********************************************************
          Get Device Id from header and valid it
         ********************************************************/
        let deviceId = req.headers.deviceid;
        if (!deviceId) {
          return res.status(401).json({ status: 0, message: i18n.__("DEVICE_ID_WITH_API") })
        }

        if (deviceId != 'ios' && deviceId != 'android' && deviceId != 'web') {
          return res.status(401).json({ status: 0, message: i18n.__("WRONG_DEVICE_ID") })
        }


        const authenticate = new Globals();

        /********************************************************
          Check token in DB
         ********************************************************/
        const tokenCheck = await authenticate.checkUserTokenInDB(token, deviceId);
        if (!tokenCheck) {
          return res.status(401).json({ status: 0, message: i18n.__("INVALID_TOKEN") })
        }

        /********************************************************
          Check token Expiration
         ********************************************************/
        const tokenExpire = await authenticate.checkUserExpiration(token, deviceId);
        if (!tokenExpire) {
          return res.status(401).json({ status: 0, message: i18n.__("TOKEN_EXPIRED") })
        }

        /********************************************************
          Check User in DB
         ********************************************************/
        const userExist = await authenticate.checkUserInDB(token);
        if (!userExist) {
          return res.status(401).json({ status: 0, message: i18n.__("USER_NOT_EXIST") })
        }

        /********************************************************
          Check User's status in DB
         ********************************************************/
        const userActive = await authenticate.checkUserStatus(userExist._id);
        if (!userActive) {
          return res.status(401).json({ status: 0, message: i18n.__("USER_IS_NOT_ACTIVE") })
        }

        if (userExist) {
          userExist.deviceId = deviceId;
          req.currentUser = userExist;
        }
        next();
      } catch (err) {
        console.error('Token authentication', err);
        return res.send({ status: 0, message: err });
      }
    };
  }
  checkUserTokenInDB(token, deviceId) {
    return new Promise(async (resolve, reject) => {
      try {
        /********************************************************
          Convert token into buffer and decode the token
         ********************************************************/
        // let tokenDetails = Buffer.from(token, 'binary').toString();
        let decoded = jwt.verify(token, config.securityToken, { ignoreExpiration: true });
        if (!decoded) {
          return resolve(false);
        }
        /********************************************************
          Check token is authorized or not
         ********************************************************/
        const authenticate = await UserAuthToken.findOne({
          where: {
            accessToken: token,
          },
        });
        if (authenticate) return resolve(true);
        return resolve(false);
      } catch (err) {
        console.error('Check token in db', err);
        return resolve({ message: err, status: 0 });
      }
    });
  }
  checkUserExpiration(token, deviceId) {
    return new Promise(async (resolve, reject) => {
      /********************************************************
           Convert token into buffer and decode the token
       ********************************************************/
      let tokenDetails = token;
      let status = false;

      /********************************************************
           Check token Expiration
       ********************************************************/
      const authenticate = await UserAuthToken.findOne({
        where: {
          accessToken: tokenDetails,
        },
        raw: true
      });
      if (authenticate) {
        let expiryDate = Moment(authenticate.tokenExpiry, 'YYYY-MM-DD HH:mm:ss');
        let now = Moment(new Date(), 'YYYY-MM-DD HH:mm:ss');
        if (expiryDate > now) {
          status = true;
          resolve(status);
        }
      }
      resolve(status);
    });
  }
  checkUserStatus(_id) {
    return new Promise(async (resolve, reject) => {
      try {
        /********************************************************
          Check User Status in DB
         ********************************************************/
        const user = await appUserSchema.findOne({ where: { _id: _id, isDeleted: false, status: true }, raw: true });
        if (user) {
          return resolve(true);
        }
        return resolve(false);
      } catch (err) {
        console.error('Check User in db');
        return reject({ message: err, status: 0 });
      }
    });
  }
  checkUserInDB(token) {
    return new Promise(async (resolve, reject) => {
      try {
        /********************************************************
          Decode token
         ********************************************************/
        let decoded = jwt.decode(token);
        if (!decoded) {
          return resolve(false);
        }
        let userId = decoded.id;
        /********************************************************
          Check admin in DB
         ********************************************************/
        const user = await appUserSchema.findOne({ where: { _id: userId, isDeleted: false }, raw: true });
        if (user) {
          return resolve(user);
        }
        return resolve(false);
      } catch (err) {
        console.error('Check ADMIN in db');
        return reject({ message: err, status: 0 });
      }
    });
  }
  getUserToken(params) {
    return new Promise(async (resolve, reject) => {
      try {
        // Generate Token
        let token = jwt.sign({
          id: params.id,
          algorithm: "HS256",
          exp: Math.floor(Date.now() / 1000) + parseInt(config.tokenExpiry)
        }, config.securityToken);
        params.token = token;
        params.userId = params.id;
        let tokenExpiryTime = Moment().add(parseInt(config.tokenExpirationTime), 'minutes');
        delete params.id
        let fetchUser = await UserAuthToken.findOne({
          where: {
            userId: params.userId
          }, raw: true
        });
        if (fetchUser) {
          await UserAuthToken.update({ 'accessToken': token, 'tokenExpiry': tokenExpiryTime, 'deviceId': params.deviceId }, {
            where: { userId: params.userId },
            // fields: this.req.body,
            returning: true,
          });
        }
        else {
          let createData = {
            'userId': params.userId,
            'accessToken': token,
            'tokenExpiry': tokenExpiryTime,
            'deviceId': params.deviceId
          }
          await UserAuthToken.create(createData)
        }
        return resolve(token);
      } catch (err) {
        console.log("Get token", err);
        return reject({ message: err, status: 0 });
      }
    });
  }
}

module.exports = Globals;
