const config = require('./configs');
const ioRedis = require("ioredis");
const { createClient } = require("redis");
const {logger} = require('../app/services/logger');
let redisClient, pubClient, subClient;

const redisConnection = async () => {
  try {
    const redisOptions = {
      host: config.REDIS_HOST,
      port: config.REDIS_PORT,
      //password: config.REDIS_PASSWORD,
      database: config.REDIS_DATABASE_NUMBER,
    };

    const pubSubRedisOptions = {
      host: config.PUBSUB_REDIS_HOST,
      port: config.PUBSUB_REDIS_PORT,
      //password: config.PUBSUB_REDIS_PASSWORD,
      db: config.PUBSUB_REDIS_DATABASE_NUMBER,
    };

    // Create connections
    pubClient = new ioRedis(pubSubRedisOptions);
    subClient = new ioRedis(pubSubRedisOptions);
    redisClient = createClient(redisOptions);

    // Set up error handling
    redisClient.on('error', (error) => {
      console.log('Redis Error:', error);
      
    });

    // Connect
    await redisClient.connect().then(() =>{
      redisClient.flushDb();
      console.log('Redis Flush Database successfully!');
    })
    
    console.log('Redis Connected successfully!');
    return redisClient;
  } catch (error) {
    console.log('Redis Connection Error:', error);
    throw error;
  }
};

// Initialize connection when module is loaded
redisConnection().catch(console.error);

module.exports = { redisConnection, redisClient, pubClient, subClient };

// Export default for easier use
module.exports.default = redisClient;