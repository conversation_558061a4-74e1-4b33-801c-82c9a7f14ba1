/****************************
 POSTGRESQL SEQUELIZE CONNECTION
 ****************************/
 const config = require('./configs');
 const { Sequelize } = require('sequelize');
 const glob = require('glob');
 
 const sequelizeConnection = new Sequelize(config.db, {
   logging: false,
   sync: true,
 });
 
 const databaseConnection = async () => {
   try {
     // Wait for the connection to be established
     await sequelizeConnection.authenticate();
     console.log('-------- Connection to Database has been established successfully :) --------');
 
     // Load all model schemas and set up relationships
     let db = {};
     const modules = '/../../app/modules';
     const schemaFiles = glob.sync(__dirname + modules + '/**/*Schema.js');
     schemaFiles.forEach((schema) => {
       const models = require(schema);
       db = { ...db, ...models };
     });
 
     // Set up associations for the models
     Object.keys(db).forEach((modelName) => {
       if (modelName && db[modelName] && db[modelName].associate) {
         db[modelName].associate(db);
       }
     });
 
     // Synchronize the models (create tables if they don't exist)
     await sequelizeConnection.sync();
     //console.log('-------- Database tables created (if necessary) based on models. --------');
     
     return sequelizeConnection;
   } catch (error) {
     console.error('Unable to connect to the database :( \n', error);
     throw error; // Throw the error to handle it in the calling function
   }
 };
 
 module.exports = { sequelizeConnection, databaseConnection };
 