/****************************
 EXPRESS AND ROUTING HANDLING
 ****************************/

const express = require("express");
const morgan = require("morgan");
const morganBody = require("morgan-body");
const compress = require("compression");
const bodyParser = require("body-parser");
const methodOverride = require("method-override");
const session = require("express-session");
const cors = require("cors"); //For cross domain error
const fs = require("fs");
const path = require("path");
const timeout = require("connect-timeout");
const { glob } = require("glob");

const config = require("./configs");
const { WHITELISTED_URLS } = require("../app/services/constant");

module.exports = function () {
  console.log("env - " + process.env.NODE_ENV);
  const app = express();

  // create a write stream (in append mode)
  const accessLogStream = fs.createWriteStream(
    path.join(__dirname, "access.log"),
    { flags: "a" }
  );

  // setup the logger
  // app.use(morgan('combined', { stream: accessLogStream }))
  app.use(
    morgan(":remote-addr :method :url :status - :date", {
      stream: accessLogStream,
    })
  );

  //console.log(__dirname)
  if (process.env.NODE_ENV === "development") {
    app.use(morgan("dev"));
  } else if (process.env.NODE_ENV === "production") {
    app.use(compress({ threshold: 2 }));
  }

  app.use(
    bodyParser.urlencoded({
      limit: "50mb",
      extended: true,
    })
  );

  app.use(bodyParser.json());
  morganBody(app, {
    logReqDateTime: false,
    logReqUserAgent: false,
    theme: "dimmed",
    stream: accessLogStream,
  });

  app.use(methodOverride());

  app.use(cors());

  // =======   Settings for CORS
  app.use((req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept"
    );
    next();
  });

  // let corsOptions = {
  //   origin: WHITELISTED_URLS, // Compliant
  // };
  // app.use(cors(corsOptions));

  // // =======   Settings for CORS
  // app.use((req, res, next) => {
  //   const origin = req.header('Origin');

  //   if (WHITELISTED_URLS.indexOf(origin) >= 0) {
  //     res.setHeader('Access-Control-Allow-Origin', origin);
  //   }
  //   res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  //   next();
  // });

  app.use(timeout(120000));
  app.use(haltOnTimedout);

  function haltOnTimedout(req, res, next) {
    if (!req.timedout) next();
  }

  app.use((err, req, res, next) => {
    return res.send({
      status: 0,
      statusCode: 500,
      message: err.message,
      error: err,
    });
  });

  app.use(
    session({
      cookie: { maxAge: 30000 },
      saveUninitialized: true,
      resave: true,
      secret: config.sessionSecret,
    })
  );

  app.use(express.json());

  // =======   Routing
  (async () => {
    const modulesPath = path.resolve(__dirname, "..", "app", "modules");
    const routingFiles = await glob(`${modulesPath}/**/*Routes.js`);

    routingFiles.forEach((route) => {
      const absolutePath = path.resolve(route);
      const stats = fs.statSync(absolutePath);
      const fileSizeInBytes = stats.size;
      if (fileSizeInBytes) {
        const routeModule = require(absolutePath);
        if (typeof routeModule === "function") {
          routeModule(app, express);
        } else {
          console.error(
            `The module at ${absolutePath} does not export a function.`
          );
        }
      }
    });
  })();

  return app;
};
