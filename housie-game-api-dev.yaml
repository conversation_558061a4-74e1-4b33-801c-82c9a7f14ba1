apiVersion: apps/v1
kind: Deployment
metadata:
  name: housie-dev-api
  labels:
    app: housie-dev-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: housie-dev-api
  template:
    metadata:
      labels:
        app: housie-dev-api
    spec:
      imagePullSecrets:
        - name: creddocker
      containers:
        - name: housie-dev-api
          image: harbor.indianic.com/housie-game/nodejs-api-dev:latest
          ports:
            - containerPort: 4000
          imagePullPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: housie-dev-api
  labels:
    app: housie-dev-api
spec:
  selector:
    app: housie-dev-api
  ports:
    - port: 4000
      targetPort: 4000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: housie-dev-api-ingress
spec:
  rules:
    - host: housie-dev-api.devpress.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: housie-dev-api
                port:
                  number: 4000
