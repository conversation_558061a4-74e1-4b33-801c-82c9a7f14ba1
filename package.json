{"name": "universal-admin-nodejs-postgresql", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "sonar": "node sonar-project.js", "sonar-test": "sonar-scanner", "migrate": "sequelize db:migrate", "seed": "node app/services/seedData.js", "sync-labels": "node app/services/syncLanguageLabels.js"}, "repository": {"type": "git", "url": "http://git.indianic.com/IIL0/I2022-6239/javascript-frameworks-nodejs-postgresql.git"}, "author": "IndiaNIC", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.782.0", "@socket.io/redis-adapter": "^8.3.0", "aws-sdk": "^2.1692.0", "basic-auth": "2.0.1", "bcrypt": "^5.1.1", "body-parser": "^2.2.0", "compression": "^1.8.0", "connect-timeout": "^1.9.0", "cors": "^2.8.5", "custom-env": "^2.0.6", "express": "^5.1.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "glob": "^11.0.1", "google-auth-library": "^10.1.0", "http-auth": "4.2.0", "i18n": "^0.15.1", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "lodash": "^4.17.21", "method-override": "^3.0.0", "moment": "^2.30.1", "morgan": "^1.10.0", "morgan-body": "2.6.9", "multiparty": "4.2.3", "node-cron": "^3.0.3", "nodemon": "3.1.9", "pg": "^8.14.1", "pg-hstore": "^2.3.4", "randomstring": "^1.3.1", "redis": "^5.5.6", "redlock": "^5.0.0-beta.2", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0"}, "devDependencies": {"sequelize-cli": "6.6.2", "sonarqube-scanner": "4.3.0"}}