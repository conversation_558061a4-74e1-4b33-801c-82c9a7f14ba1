/****************************
 SERVER MAIN FILE
 ****************************/

// need to add in case of self-signed certificate connection
process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = 0;

// Include Modules
const path = require('path');
const i18n = require('i18n');
const swaggerUi = require('swagger-ui-express');
const basicAuth = require('basic-auth');
const fs = require('fs');
let exp = require('express');

const config = require('./configs/configs');
const express = require('./configs/express');
const { databaseConnection } = require('./configs/database');
databaseConnection();
const { Server } = require("socket.io");

const { redisConnection } = require('./configs/RedisConnection');
const { redlockConnection } = require('./app/services/redisLock');
const Globals = require("./configs/Globals");
const { eventCases } = require("./app/modules/EventCases/EventCases");
redisConnection();
redlockConnection();
i18n.configure({
  locales: ['en', 'es', 'de'],
  directory: __dirname + '/app/locales',
  defaultLocale: 'en',
});

// HTTP Authentication
const auth = function (req, res, next) {
  const user = basicAuth(req);
  if (!user || !user.name || !user.pass) {
    res.set('WWW-Authenticate', 'Basic realm=Authorization Required');
    res.sendStatus(401);
    return;
  }
  if (user.name === config.HTTPAuthUser && user.pass === config.HTTPAuthPassword) {
    next();
  } else {
    res.set('WWW-Authenticate', 'Basic realm=Authorization Required');
    res.sendStatus(401);
  }
};
global.appRoot = path.resolve(__dirname);

const app = express();

app.get('/', function (req, res, next) {
  res.send('Hello World');
});

app.use('/public', exp.static(__dirname + '/public'));

/*************************************************************
 * Swagger settings and migration from all internal modules. *
 *************************************************************/
if (process.env.NODE_ENV !== 'production') {
  const options = {
    customCss: '.swagger-ui .models { display: none }',
    customSiteTitle: 'Housie Game: ' + process.env.NODE_ENV,
    swaggerOptions: {
      docExpansion: 'none',
      tagsSorter: 'alpha',
    },
  };
  const mainSwaggerData = JSON.parse(fs.readFileSync('swagger.json'));

  mainSwaggerData.host = config.host;
  mainSwaggerData.basePath = config.baseApiUrl;

  const modules = './app/modules';
  fs.readdirSync(modules).forEach((file) => {
    if (fs.existsSync(modules + '/' + file + '/swagger.json')) {
      const stats = fs.statSync(modules + '/' + file + '/swagger.json');
      const fileSizeInBytes = stats.size;
      if (fileSizeInBytes) {
        let swaggerData = fs.readFileSync(modules + '/' + file + '/swagger.json');
        swaggerData = swaggerData ? JSON.parse(swaggerData) : { paths: {}, definitions: {} };

        mainSwaggerData.paths = {
          ...mainSwaggerData.paths,
          ...swaggerData.paths,
        };
        mainSwaggerData.definitions = {
          ...swaggerData.definitions,
          ...mainSwaggerData.definitions,
        };
      }
    }
  });
  if (config.isHTTPAuthForSwagger && config.isHTTPAuthForSwagger == 'true') {
    app.get('/docs', auth, (req, res, next) => {
      next();
    });
  }
  const swaggerDocument = mainSwaggerData;
  app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument, options));
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Optional: exit process if it's a critical error
  // process.exit(1);
});

// Listening Server
const server = app.listen(parseInt(config.serverPort), async () => {
  console.log('process.env.NODE_ENV', process.env.NODE_ENV);
  console.log(`Server connected with db (connection url) : ${config.db}`);
  console.log(`Server running at http://localhost:${config.serverPort}`);
});

 
const io = new Server(server, {
  cors: {
    origin: "*",
    transports: ["websocket", "polling", "xhr-polling"],
  },
});

io.use(async (socket, next) => {
  try {
    const token = socket.handshake.headers["authorization"];
    const deviceId = socket.handshake.headers["deviceid"];

    if (!token) {
      return next(new Error(i18n.__("TOKEN_WITH_API")));
    }

    if (!deviceId) {
      return next(new Error(i18n.__("DEVICE_ID_WITH_API")));
    }

    if (!["ios", "android", "web"].includes(deviceId)) {
      return next(new Error(i18n.__("WRONG_DEVICE_ID")));
    }
    const authenticate = new Globals();
    const tokenCheck = await authenticate.checkUserTokenInDB(token, deviceId);
    if (!tokenCheck) {
      return next(new Error(i18n.__("INVALID_TOKEN")));
    }

    const tokenExpire = await authenticate.checkUserExpiration(token, deviceId);
    if (!tokenExpire) {
      return next(new Error(i18n.__("TOKEN_EXPIRED")));
    }

    const userExist = await authenticate.checkUserInDB(token);
    if (!userExist) {
      return next(new Error(i18n.__("USER_NOT_EXIST")));
    }

    const userActive = await authenticate.checkUserStatus(userExist._id);
    if (!userActive) {
      return next(new Error(i18n.__("USER_IS_NOT_ACTIVE")));
    }

    userExist.deviceId = deviceId;
    socket.user = userExist;

    next(); 
  } catch (error) {
    console.error("Socket auth error:", error);
    next(new Error("Authentication error"));
  }
});

const socketIOConnection = io.on("connection", async (socket) => {
  await eventCases(socket);
});

module.exports = socketIOConnection;

