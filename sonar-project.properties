sonar.projectKey=universal-admin-postgres-node-api-dev
# sonar.projectName=Universal-Admin-postgres
# sonar.projectVersion=1.0

# sonar.sources=src,copybooks

# sonar.sourceEncoding=UTF-8

## Cobol Specific Properties

# comma-separated paths to directories with copybooks
# sonar.cobol.copy.directories=copybooks
# comma-separated list of suffixes
# sonar.cobol.file.suffixes=cbl,cpy
# sonar.cobol.copy.suffixes=cpy



## Flex Specific Properties

# retrieve code coverage data from the Cobertura report
# sonar.flex.cobertura.reportPath=coverage-report/coverage-cobertua-flex.xml



# PL/I Specific Properties
# sonar.pli.marginLeft=2
# sonar.pli.marginRight=0

# Scan settings.
#sonar.projectBaseDir=.
# Define the directories that should be scanned. Comma separated.
sonar.sources=.
sonar.inclusions=/**
#sonar.php.coverage.reportPaths=./coverage/lcov.info
#sonar.php.file.suffixes=php
#sonar.sourceEncoding=UTF-8

sonar.coverage.exclusions=/**
