{"swagger": "2.0", "info": {"version": "1.0.0", "title": "Housie Game API", "description": "Housie Game API", "license": {"name": "", "url": ""}}, "host": "localhost:4000", "basePath": "/api/", "schemes": ["http", "https"], "securityDefinitions": {"bearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "scheme": "bearer", "in": "header"}, "basicAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "scheme": "basic", "in": "header"}}, "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {}}